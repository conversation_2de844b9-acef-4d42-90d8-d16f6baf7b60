# GameReview Pro - Windows 11 Native Desktop Application
# Minimal dependencies for reliable execution

# GUI Framework - Modern, reliable, and Windows 11 compatible
customtkinter==5.2.2
pillow==10.1.0

# Database - Built-in with Python, no external dependencies
# sqlite3 is included with Python standard library

# Date/Time handling
python-dateutil==2.8.2

# Optional: For better Windows integration
pywin32==306; sys_platform == "win32"

# Development and testing
pytest==7.4.3

// Professional Game Detail Screen
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import FastImage from 'react-native-fast-image';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Game, Review, NavigationProps } from '../types';
import { colors, typography, layout, shadows } from '../theme';
import { apiService } from '../services/api';
import ReviewCard from '../components/ReviewCard';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const { width, height } = Dimensions.get('window');

export const GameDetailScreen: React.FC<NavigationProps> = ({ navigation, route }) => {
  const { gameId } = route.params;
  const [game, setGame] = useState<Game | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadGameData();
  }, [gameId]);

  const loadGameData = async () => {
    try {
      setError(null);
      
      const [gameResponse, reviewsResponse] = await Promise.all([
        apiService.getGame(gameId),
        apiService.getReviewsForGame(gameId, { limit: 10 })
      ]);

      if (gameResponse.success && gameResponse.data) {
        setGame(gameResponse.data);
      } else {
        setError('Game not found');
        return;
      }

      if (reviewsResponse.success) {
        setReviews(reviewsResponse.reviews);
      }

    } catch (error) {
      console.error('Error loading game data:', error);
      setError('Failed to load game details');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadGameData();
  };

  const navigateToReview = (review: Review) => {
    navigation.navigate('ReviewDetail', { reviewId: review.id });
  };

  const navigateToWriteReview = () => {
    navigation.navigate('WriteReview', { gameId: game!.id });
  };

  const likeReview = async (reviewId: number) => {
    try {
      const response = await apiService.likeReview(reviewId);
      if (response.success) {
        // Update the review in the list
        setReviews(reviews.map(review => 
          review.id === reviewId 
            ? { ...review, likes_count: response.data?.likes_count || review.likes_count + 1 }
            : review
        ));
      }
    } catch (error) {
      console.error('Error liking review:', error);
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<Icon key={i} name="star" size={20} color={colors.gold} />);
      } else if (i === fullStars && hasHalfStar) {
        stars.push(<Icon key={i} name="star-half" size={20} color={colors.gold} />);
      } else {
        stars.push(<Icon key={i} name="star-border" size={20} color={colors.lightGray} />);
      }
    }
    return stars;
  };

  const getPlatformColor = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'pc': return colors.amber;
      case 'playstation 4':
      case 'playstation 5': return '#003087';
      case 'xbox one':
      case 'xbox series x/s': return '#107C10';
      case 'nintendo switch': return '#E60012';
      case 'ios':
      case 'android': return colors.green;
      default: return colors.primaryBlue;
    }
  };

  if (loading) {
    return <LoadingSpinner message="Loading game details..." />;
  }

  if (error || !game) {
    return <ErrorMessage message={error || 'Game not found'} onRetry={loadGameData} />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primaryBlue}
            colors={[colors.primaryBlue]}
          />
        }
      >
        {/* Hero Section */}
        <View style={styles.heroContainer}>
          <FastImage
            source={{
              uri: game.cover_image || 'https://via.placeholder.com/400x600/1A1A1A/FFFFFF?text=No+Image',
              priority: FastImage.priority.high,
            }}
            style={styles.coverImage}
            resizeMode={FastImage.resizeMode.cover}
          />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.8)', colors.black]}
            style={styles.heroOverlay}
          >
            <View style={styles.heroContent}>
              <Text style={styles.gameTitle}>{game.title}</Text>
              <Text style={styles.gameGenre}>{game.genre}</Text>
              
              {/* Rating */}
              {game.average_rating > 0 && (
                <View style={styles.ratingContainer}>
                  <View style={styles.stars}>
                    {renderStars(game.average_rating)}
                  </View>
                  <Text style={styles.ratingText}>
                    {game.average_rating.toFixed(1)} ({game.total_reviews} reviews)
                  </Text>
                </View>
              )}
            </View>
          </LinearGradient>
        </View>

        {/* Game Info */}
        <View style={styles.infoSection}>
          {/* Platforms */}
          <View style={styles.platformsContainer}>
            <Text style={styles.sectionTitle}>Available On</Text>
            <View style={styles.platformsList}>
              {game.platforms.map((platform, index) => (
                <View
                  key={index}
                  style={[styles.platformBadge, { backgroundColor: getPlatformColor(platform) }]}
                >
                  <Text style={styles.platformText}>{platform}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Description */}
          <View style={styles.descriptionContainer}>
            <Text style={styles.sectionTitle}>About</Text>
            <Text style={styles.description}>{game.description}</Text>
          </View>

          {/* Stats */}
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Icon name="star" size={24} color={colors.gold} />
              <Text style={styles.statNumber}>
                {game.average_rating > 0 ? game.average_rating.toFixed(1) : 'N/A'}
              </Text>
              <Text style={styles.statLabel}>Rating</Text>
            </View>
            <View style={styles.statItem}>
              <Icon name="rate-review" size={24} color={colors.primaryBlue} />
              <Text style={styles.statNumber}>{game.total_reviews}</Text>
              <Text style={styles.statLabel}>Reviews</Text>
            </View>
            <View style={styles.statItem}>
              <Icon name="devices" size={24} color={colors.green} />
              <Text style={styles.statNumber}>{game.platforms.length}</Text>
              <Text style={styles.statLabel}>Platforms</Text>
            </View>
          </View>
        </View>

        {/* Write Review Button */}
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={styles.writeReviewButton}
            onPress={navigateToWriteReview}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={[colors.primaryBlue, colors.cyan]}
              style={styles.buttonGradient}
            >
              <Icon name="rate-review" size={24} color={colors.white} />
              <Text style={styles.buttonText}>Write a Review</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* Reviews Section */}
        <View style={styles.reviewsSection}>
          <View style={styles.reviewsHeader}>
            <Text style={styles.sectionTitle}>
              Reviews ({reviews.length})
            </Text>
          </View>

          {reviews.length > 0 ? (
            reviews.map((review) => (
              <ReviewCard
                key={review.id}
                review={review}
                onPress={navigateToReview}
                onLike={likeReview}
                style={styles.reviewCard}
              />
            ))
          ) : (
            <View style={styles.noReviewsContainer}>
              <Icon name="rate-review" size={48} color={colors.lightGray} />
              <Text style={styles.noReviewsTitle}>No Reviews Yet</Text>
              <Text style={styles.noReviewsText}>
                Be the first to share your experience with this game!
              </Text>
              <TouchableOpacity
                style={styles.firstReviewButton}
                onPress={navigateToWriteReview}
              >
                <Text style={styles.firstReviewButtonText}>Write First Review</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  scrollView: {
    flex: 1,
  },
  heroContainer: {
    height: height * 0.6,
    position: 'relative',
  },
  coverImage: {
    width: '100%',
    height: '100%',
  },
  heroOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%',
    justifyContent: 'flex-end',
  },
  heroContent: {
    padding: layout.screenPadding,
    paddingBottom: layout.xl,
  },
  gameTitle: {
    ...typography.h1,
    color: colors.white,
    fontWeight: '700',
    marginBottom: layout.xs,
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  gameGenre: {
    ...typography.body,
    color: colors.primaryBlue,
    fontWeight: '600',
    marginBottom: layout.md,
  },
  ratingContainer: {
    alignItems: 'flex-start',
  },
  stars: {
    flexDirection: 'row',
    marginBottom: layout.xs,
  },
  ratingText: {
    ...typography.body,
    color: colors.white,
    fontWeight: '500',
  },
  infoSection: {
    padding: layout.screenPadding,
  },
  sectionTitle: {
    ...typography.h3,
    color: colors.white,
    marginBottom: layout.md,
  },
  platformsContainer: {
    marginBottom: layout.lg,
  },
  platformsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: layout.sm,
  },
  platformBadge: {
    paddingHorizontal: layout.md,
    paddingVertical: layout.sm,
    borderRadius: layout.borderRadius,
  },
  platformText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '600',
  },
  descriptionContainer: {
    marginBottom: layout.lg,
  },
  description: {
    ...typography.body,
    color: colors.lightGray,
    lineHeight: 24,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    paddingVertical: layout.lg,
    ...shadows.small,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    ...typography.h2,
    color: colors.white,
    fontWeight: '700',
    marginTop: layout.sm,
  },
  statLabel: {
    ...typography.caption,
    color: colors.lightGray,
    marginTop: layout.xs,
  },
  actionContainer: {
    paddingHorizontal: layout.screenPadding,
    marginBottom: layout.lg,
  },
  writeReviewButton: {
    borderRadius: layout.borderRadius,
    overflow: 'hidden',
    ...shadows.medium,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: layout.md,
    gap: layout.sm,
  },
  buttonText: {
    ...typography.button,
    color: colors.white,
  },
  reviewsSection: {
    paddingHorizontal: layout.screenPadding,
    paddingBottom: layout.xl,
  },
  reviewsHeader: {
    marginBottom: layout.md,
  },
  reviewCard: {
    marginBottom: layout.md,
  },
  noReviewsContainer: {
    alignItems: 'center',
    paddingVertical: layout.xl,
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    ...shadows.small,
  },
  noReviewsTitle: {
    ...typography.h3,
    color: colors.white,
    marginTop: layout.md,
    marginBottom: layout.sm,
  },
  noReviewsText: {
    ...typography.body,
    color: colors.lightGray,
    textAlign: 'center',
    marginBottom: layout.lg,
    paddingHorizontal: layout.lg,
  },
  firstReviewButton: {
    backgroundColor: colors.primaryBlue,
    paddingHorizontal: layout.lg,
    paddingVertical: layout.sm,
    borderRadius: layout.borderRadius,
  },
  firstReviewButtonText: {
    ...typography.button,
    color: colors.white,
  },
});

export default GameDetailScreen;

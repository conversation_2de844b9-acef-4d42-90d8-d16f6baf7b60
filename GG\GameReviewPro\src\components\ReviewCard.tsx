// Professional Review Card Component
import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Review } from '../types';
import { colors, typography, shadows, layout } from '../theme';

interface ReviewCardProps {
  review: Review;
  onPress: (review: Review) => void;
  onLike?: (reviewId: number) => void;
  showGameInfo?: boolean;
  style?: any;
}

const { width } = Dimensions.get('window');

export const ReviewCard: React.FC<ReviewCardProps> = ({ 
  review, 
  onPress, 
  onLike,
  showGameInfo = false,
  style 
}) => {
  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 0; i < 5; i++) {
      stars.push(
        <Icon 
          key={i} 
          name={i < rating ? "star" : "star-border"} 
          size={16} 
          color={colors.gold} 
        />
      );
    }
    return stars;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  const getPlatformColor = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'pc': return colors.amber;
      case 'playstation 4':
      case 'playstation 5': return '#003087';
      case 'xbox one':
      case 'xbox series x/s': return '#107C10';
      case 'nintendo switch': return '#E60012';
      case 'ios':
      case 'android': return colors.green;
      default: return colors.primaryBlue;
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={() => onPress(review)}
      activeOpacity={0.8}
    >
      <View style={styles.card}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.userInfo}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>
                {review.reviewer_name.charAt(0).toUpperCase()}
              </Text>
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>{review.reviewer_name}</Text>
              <Text style={styles.reviewDate}>{formatDate(review.created_at)}</Text>
            </View>
          </View>
          
          {review.platform && (
            <View style={[styles.platformBadge, { backgroundColor: getPlatformColor(review.platform) }]}>
              <Text style={styles.platformText}>{review.platform}</Text>
            </View>
          )}
        </View>

        {/* Game Info (if showing) */}
        {showGameInfo && review.game_title && (
          <View style={styles.gameInfo}>
            <FastImage
              source={{
                uri: review.game_cover || 'https://via.placeholder.com/60x80/1A1A1A/FFFFFF?text=Game',
                priority: FastImage.priority.low,
              }}
              style={styles.gameImage}
              resizeMode={FastImage.resizeMode.cover}
            />
            <Text style={styles.gameTitle}>{review.game_title}</Text>
          </View>
        )}

        {/* Rating */}
        <View style={styles.ratingContainer}>
          <View style={styles.stars}>
            {renderStars(review.rating)}
          </View>
          <Text style={styles.ratingText}>{review.rating}/5</Text>
        </View>

        {/* Review Text */}
        {review.review_text && (
          <Text style={styles.reviewText} numberOfLines={4}>
            {review.review_text}
          </Text>
        )}

        {/* Pros and Cons */}
        {(review.pros.length > 0 || review.cons.length > 0) && (
          <View style={styles.prosConsContainer}>
            {review.pros.length > 0 && (
              <View style={styles.prosContainer}>
                <View style={styles.prosConsHeader}>
                  <Icon name="thumb-up" size={14} color={colors.green} />
                  <Text style={styles.prosConsTitle}>Pros</Text>
                </View>
                {review.pros.slice(0, 2).map((pro, index) => (
                  <Text key={index} style={styles.prosConsItem}>• {pro}</Text>
                ))}
              </View>
            )}
            
            {review.cons.length > 0 && (
              <View style={styles.consContainer}>
                <View style={styles.prosConsHeader}>
                  <Icon name="thumb-down" size={14} color={colors.red} />
                  <Text style={styles.prosConsTitle}>Cons</Text>
                </View>
                {review.cons.slice(0, 2).map((con, index) => (
                  <Text key={index} style={styles.prosConsItem}>• {con}</Text>
                ))}
              </View>
            )}
          </View>
        )}

        {/* Review Image */}
        {review.image_path && (
          <FastImage
            source={{ uri: review.image_path }}
            style={styles.reviewImage}
            resizeMode={FastImage.resizeMode.cover}
          />
        )}

        {/* Footer */}
        <View style={styles.footer}>
          <View style={styles.stats}>
            <View style={styles.statItem}>
              <Icon name="visibility" size={16} color={colors.lightGray} />
              <Text style={styles.statText}>{review.views_count}</Text>
            </View>
          </View>
          
          <TouchableOpacity
            style={styles.likeButton}
            onPress={() => onLike?.(review.id)}
            activeOpacity={0.7}
          >
            <Icon name="thumb-up" size={16} color={colors.primaryBlue} />
            <Text style={styles.likeText}>{review.likes_count}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: layout.md,
  },
  card: {
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    padding: layout.md,
    ...shadows.small,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: layout.sm,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryBlue,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: layout.sm,
  },
  avatarText: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    ...typography.body,
    color: colors.white,
    fontWeight: '500',
  },
  reviewDate: {
    ...typography.caption,
    color: colors.lightGray,
  },
  platformBadge: {
    paddingHorizontal: layout.sm,
    paddingVertical: layout.xs / 2,
    borderRadius: layout.borderRadius / 2,
  },
  platformText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '500',
  },
  gameInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: layout.sm,
    padding: layout.sm,
    backgroundColor: colors.mediumGray,
    borderRadius: layout.borderRadius / 2,
  },
  gameImage: {
    width: 40,
    height: 50,
    borderRadius: layout.borderRadius / 2,
    marginRight: layout.sm,
  },
  gameTitle: {
    ...typography.body,
    color: colors.white,
    fontWeight: '500',
    flex: 1,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: layout.sm,
  },
  stars: {
    flexDirection: 'row',
    marginRight: layout.sm,
  },
  ratingText: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
  reviewText: {
    ...typography.body,
    color: colors.lightGray,
    lineHeight: 22,
    marginBottom: layout.sm,
  },
  prosConsContainer: {
    marginBottom: layout.sm,
  },
  prosContainer: {
    marginBottom: layout.xs,
  },
  consContainer: {
    marginBottom: layout.xs,
  },
  prosConsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: layout.xs / 2,
  },
  prosConsTitle: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '600',
    marginLeft: layout.xs / 2,
  },
  prosConsItem: {
    ...typography.caption,
    color: colors.lightGray,
    marginLeft: layout.md,
  },
  reviewImage: {
    width: '100%',
    height: 200,
    borderRadius: layout.borderRadius / 2,
    marginBottom: layout.sm,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: layout.md,
  },
  statText: {
    ...typography.caption,
    color: colors.lightGray,
    marginLeft: layout.xs / 2,
  },
  likeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: layout.sm,
    paddingVertical: layout.xs / 2,
    borderRadius: layout.borderRadius / 2,
    backgroundColor: colors.mediumGray,
  },
  likeText: {
    ...typography.caption,
    color: colors.primaryBlue,
    marginLeft: layout.xs / 2,
    fontWeight: '500',
  },
});

export default ReviewCard;

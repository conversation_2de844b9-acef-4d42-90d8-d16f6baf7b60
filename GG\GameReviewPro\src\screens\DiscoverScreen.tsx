// Professional Discover Screen
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Game, NavigationProps } from '../types';
import { colors, typography, layout, shadows } from '../theme';
import { apiService } from '../services/api';
import GameCard from '../components/GameCard';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

export const DiscoverScreen: React.FC<NavigationProps> = ({ navigation }) => {
  const [games, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGenre, setSelectedGenre] = useState<string | null>(null);
  const [selectedSort, setSelectedSort] = useState('title');
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const genres = [
    'All', 'Action/Adventure', 'RPG', 'Shooter', 'Sports', 'Strategy', 
    'Simulation', 'Racing', 'Fighting', 'Horror', 'Indie', 'Platformer'
  ];

  const sortOptions = [
    { label: 'Title A-Z', value: 'title' },
    { label: 'Rating High', value: 'rating' },
    { label: 'Most Reviews', value: 'reviews' },
  ];

  useEffect(() => {
    loadGames(true);
  }, [selectedGenre, selectedSort, searchQuery]);

  const loadGames = async (reset = false) => {
    try {
      setError(null);
      if (reset) {
        setLoading(true);
        setPage(1);
      }

      const currentPage = reset ? 1 : page;
      let response;

      if (searchQuery.trim()) {
        response = await apiService.searchGames(searchQuery.trim());
        if (response.success && response.data) {
          setGames(reset ? response.data : [...games, ...response.data]);
          setHasMore(false); // Search doesn't support pagination
        }
      } else {
        const params = {
          page: currentPage,
          limit: 20,
          genre: selectedGenre && selectedGenre !== 'All' ? selectedGenre : undefined,
          sort: selectedSort,
        };

        response = await apiService.getGames(params);
        if (response.success) {
          const newGames = response.games;
          setGames(reset ? newGames : [...games, ...newGames]);
          setHasMore(currentPage < response.pagination.pages);
          if (!reset) {
            setPage(currentPage + 1);
          }
        }
      }

    } catch (error) {
      console.error('Error loading games:', error);
      setError('Failed to load games. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadGames(true);
  };

  const loadMore = () => {
    if (!loading && hasMore && !searchQuery.trim()) {
      loadGames(false);
    }
  };

  const navigateToGame = (game: Game) => {
    navigation.navigate('GameDetail', { gameId: game.id });
  };

  const clearSearch = () => {
    setSearchQuery('');
  };

  const renderGameItem = ({ item }: { item: Game }) => (
    <GameCard
      game={item}
      onPress={navigateToGame}
      style={styles.gameCard}
    />
  );

  const renderGenreFilter = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={[
        styles.genreChip,
        selectedGenre === item && styles.genreChipSelected
      ]}
      onPress={() => setSelectedGenre(item === 'All' ? null : item)}
    >
      <Text style={[
        styles.genreText,
        selectedGenre === item && styles.genreTextSelected
      ]}>
        {item}
      </Text>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color={colors.lightGray} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search games..."
          placeholderTextColor={colors.lightGray}
          value={searchQuery}
          onChangeText={setSearchQuery}
          returnKeyType="search"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
            <Icon name="clear" size={20} color={colors.lightGray} />
          </TouchableOpacity>
        )}
      </View>

      {/* Genre Filters */}
      <FlatList
        data={genres}
        renderItem={renderGenreFilter}
        keyExtractor={(item) => item}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.genreList}
        style={styles.genreContainer}
      />

      {/* Sort Options */}
      <View style={styles.sortContainer}>
        <Text style={styles.sortLabel}>Sort by:</Text>
        <View style={styles.sortOptions}>
          {sortOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.sortChip,
                selectedSort === option.value && styles.sortChipSelected
              ]}
              onPress={() => setSelectedSort(option.value)}
            >
              <Text style={[
                styles.sortText,
                selectedSort === option.value && styles.sortTextSelected
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Results Count */}
      <Text style={styles.resultsCount}>
        {games.length} game{games.length !== 1 ? 's' : ''} found
      </Text>
    </View>
  );

  if (loading && games.length === 0) {
    return <LoadingSpinner message="Discovering games..." />;
  }

  if (error && games.length === 0) {
    return <ErrorMessage message={error} onRetry={() => loadGames(true)} />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={games}
        renderItem={renderGameItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={2}
        columnWrapperStyle={styles.row}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primaryBlue}
            colors={[colors.primaryBlue]}
          />
        }
        onEndReached={loadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={
          loading && games.length > 0 ? (
            <LoadingSpinner message="Loading more games..." size="small" />
          ) : null
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  header: {
    paddingHorizontal: layout.screenPadding,
    paddingBottom: layout.md,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    paddingHorizontal: layout.md,
    marginBottom: layout.md,
    ...shadows.small,
  },
  searchIcon: {
    marginRight: layout.sm,
  },
  searchInput: {
    flex: 1,
    height: 48,
    ...typography.body,
    color: colors.white,
  },
  clearButton: {
    padding: layout.xs,
  },
  genreContainer: {
    marginBottom: layout.md,
  },
  genreList: {
    paddingRight: layout.screenPadding,
  },
  genreChip: {
    paddingHorizontal: layout.md,
    paddingVertical: layout.sm,
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius * 2,
    marginRight: layout.sm,
    borderWidth: 1,
    borderColor: colors.mediumGray,
  },
  genreChipSelected: {
    backgroundColor: colors.primaryBlue,
    borderColor: colors.primaryBlue,
  },
  genreText: {
    ...typography.caption,
    color: colors.lightGray,
    fontWeight: '500',
  },
  genreTextSelected: {
    color: colors.white,
  },
  sortContainer: {
    marginBottom: layout.md,
  },
  sortLabel: {
    ...typography.body,
    color: colors.white,
    marginBottom: layout.sm,
    fontWeight: '500',
  },
  sortOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: layout.sm,
  },
  sortChip: {
    paddingHorizontal: layout.md,
    paddingVertical: layout.sm,
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    borderWidth: 1,
    borderColor: colors.mediumGray,
  },
  sortChipSelected: {
    backgroundColor: colors.primaryBlue,
    borderColor: colors.primaryBlue,
  },
  sortText: {
    ...typography.caption,
    color: colors.lightGray,
    fontWeight: '500',
  },
  sortTextSelected: {
    color: colors.white,
  },
  resultsCount: {
    ...typography.caption,
    color: colors.lightGray,
    marginBottom: layout.md,
  },
  listContent: {
    paddingBottom: layout.xl,
  },
  row: {
    justifyContent: 'space-between',
    paddingHorizontal: layout.screenPadding,
  },
  gameCard: {
    marginBottom: layout.md,
  },
});

export default DiscoverScreen;

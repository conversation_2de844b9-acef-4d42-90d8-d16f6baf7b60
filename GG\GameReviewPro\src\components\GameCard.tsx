// Professional Game Card Component
import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Game } from '../types';
import { colors, typography, shadows, layout } from '../theme';

interface GameCardProps {
  game: Game;
  onPress: (game: Game) => void;
  style?: any;
}

const { width } = Dimensions.get('window');
const cardWidth = (width - layout.screenPadding * 3) / 2;

export const GameCard: React.FC<GameCardProps> = ({ game, onPress, style }) => {
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Icon key={i} name="star" size={14} color={colors.gold} />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <Icon key={i} name="star-half" size={14} color={colors.gold} />
        );
      } else {
        stars.push(
          <Icon key={i} name="star-border" size={14} color={colors.lightGray} />
        );
      }
    }
    return stars;
  };

  const getPlatformIcon = (platforms: string[]) => {
    if (platforms.includes('PC')) return 'computer';
    if (platforms.includes('PlayStation 5') || platforms.includes('PlayStation 4')) return 'sports-esports';
    if (platforms.includes('Xbox Series X/S') || platforms.includes('Xbox One')) return 'videogame-asset';
    if (platforms.includes('Nintendo Switch')) return 'games';
    if (platforms.includes('iOS') || platforms.includes('Android')) return 'smartphone';
    return 'videogame-asset';
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={() => onPress(game)}
      activeOpacity={0.8}
    >
      <View style={styles.card}>
        {/* Game Cover Image */}
        <View style={styles.imageContainer}>
          <FastImage
            source={{
              uri: game.cover_image || 'https://via.placeholder.com/300x400/1A1A1A/FFFFFF?text=No+Image',
              priority: FastImage.priority.normal,
            }}
            style={styles.coverImage}
            resizeMode={FastImage.resizeMode.cover}
          />
          
          {/* Rating Overlay */}
          {game.average_rating > 0 && (
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.8)']}
              style={styles.ratingOverlay}
            >
              <View style={styles.ratingContainer}>
                <View style={styles.starsContainer}>
                  {renderStars(game.average_rating)}
                </View>
                <Text style={styles.ratingText}>
                  {game.average_rating.toFixed(1)}
                </Text>
              </View>
            </LinearGradient>
          )}

          {/* Platform Icon */}
          <View style={styles.platformIcon}>
            <Icon 
              name={getPlatformIcon(game.platforms)} 
              size={16} 
              color={colors.white} 
            />
          </View>
        </View>

        {/* Game Info */}
        <View style={styles.infoContainer}>
          <Text style={styles.title} numberOfLines={2}>
            {game.title}
          </Text>
          
          <Text style={styles.genre} numberOfLines={1}>
            {game.genre}
          </Text>

          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Icon name="rate-review" size={12} color={colors.lightGray} />
              <Text style={styles.statText}>
                {game.total_reviews}
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Icon name="devices" size={12} color={colors.lightGray} />
              <Text style={styles.statText}>
                {game.platforms.length}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: cardWidth,
    marginBottom: layout.md,
  },
  card: {
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    overflow: 'hidden',
    ...shadows.medium,
  },
  imageContainer: {
    position: 'relative',
    height: cardWidth * 1.4, // 1.4 aspect ratio for game covers
  },
  coverImage: {
    width: '100%',
    height: '100%',
  },
  ratingOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 40,
    justifyContent: 'flex-end',
    paddingHorizontal: layout.sm,
    paddingBottom: layout.xs,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  starsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '600',
  },
  platformIcon: {
    position: 'absolute',
    top: layout.sm,
    right: layout.sm,
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoContainer: {
    padding: layout.sm,
  },
  title: {
    ...typography.body,
    fontWeight: '600',
    color: colors.white,
    marginBottom: layout.xs / 2,
  },
  genre: {
    ...typography.caption,
    color: colors.primaryBlue,
    marginBottom: layout.xs,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    ...typography.caption,
    color: colors.lightGray,
    marginLeft: layout.xs / 2,
  },
});

export default GameCard;

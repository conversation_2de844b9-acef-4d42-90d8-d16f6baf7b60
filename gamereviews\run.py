#!/usr/bin/env python3
"""
GameReview Pro - Professional Flask Application
Entry point for the application
"""

import os
from app import create_app
from app.config import config

def main():
    """Main application entry point"""
    
    # Get configuration from environment
    config_name = os.environ.get('FLASK_ENV', 'development')
    
    # Create app with specified configuration
    app = create_app(config[config_name])
    
    # Load sample data if in development mode
    if config_name == 'development':
        try:
            with app.app_context():
                from data.sample_data import populate_sample_data
                populate_sample_data()
                print("✅ Sample data loaded successfully!")
        except ImportError:
            print("⚠️  sample_data.py not found - skipping sample data population")
        except Exception as e:
            print(f"⚠️  Error loading sample data: {e}")
    
    # Print startup information
    print("\n" + "="*60)
    print("🎮 GameReview Pro - Professional Flask Application")
    print("="*60)
    print(f"📊 Environment: {config_name}")
    print(f"🌐 API Base URL: http://localhost:5000/api")
    print(f"📁 Static Files: http://localhost:5000/static")
    print("="*60)
    print("🚀 Starting server...")
    print("="*60 + "\n")
    
    # Run the application
    app.run(
        debug=app.config.get('DEBUG', False),
        host='0.0.0.0',
        port=5000
    )

if __name__ == '__main__':
    main()

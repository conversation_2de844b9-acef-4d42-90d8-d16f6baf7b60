// Configuration for GameReview Pro Mobile App

export const API_CONFIG = {
  // Development API URL
  BASE_URL: __DEV__ ? 'http://localhost:5000/api' : 'https://your-production-api.com/api',
  
  // Request timeout in milliseconds
  TIMEOUT: 10000,
  
  // Retry configuration
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
};

export const APP_CONFIG = {
  // App information
  NAME: 'GameReview Pro',
  VERSION: '1.0.0',
  
  // Pagination
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 50,
  
  // Image upload
  MAX_IMAGE_SIZE: 16 * 1024 * 1024, // 16MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  
  // Cache settings
  CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
  
  // Animation durations
  ANIMATION_DURATION: {
    FAST: 200,
    NORMAL: 300,
    SLOW: 500,
  },
};

export const STORAGE_KEYS = {
  USER_PREFERENCES: '@gamereviews:user_preferences',
  CACHED_GAMES: '@gamereviews:cached_games',
  CACHED_REVIEWS: '@gamereviews:cached_reviews',
  LAST_SYNC: '@gamereviews:last_sync',
};

export const NAVIGATION_CONFIG = {
  // Tab bar configuration
  TAB_BAR_HEIGHT: 80,
  HEADER_HEIGHT: 60,
  
  // Screen names
  SCREENS: {
    MAIN_TABS: 'MainTabs',
    HOME: 'Home',
    DISCOVER: 'Discover',
    WRITE_REVIEW: 'WriteReview',
    PROFILE: 'Profile',
    GAME_DETAIL: 'GameDetail',
    REVIEW_DETAIL: 'ReviewDetail',
  },
};

export const GAMING_PLATFORMS = [
  'PC',
  'PlayStation 5',
  'PlayStation 4',
  'Xbox Series X/S',
  'Xbox One',
  'Nintendo Switch',
  'iOS',
  'Android',
  'Steam Deck',
  'Epic Games Store',
  'Steam',
];

export const GAME_GENRES = [
  'Action',
  'Adventure',
  'RPG',
  'Strategy',
  'Sports',
  'Racing',
  'Simulation',
  'Puzzle',
  'Fighting',
  'Shooter',
  'Horror',
  'Platformer',
  'MMORPG',
  'Battle Royale',
  'Indie',
];

export default {
  API_CONFIG,
  APP_CONFIG,
  STORAGE_KEYS,
  NAVIGATION_CONFIG,
  GAMING_PLATFORMS,
  GAME_GENRES,
};

"""
Sample Data Population for GameReview Mobile App
Pre-loads popular games and sample reviews for testing
"""

import sqlite3
from datetime import datetime, timedelta
import random

def get_db_connection():
    """Get database connection"""
    from app.database import get_db_connection as get_conn
    return get_conn()

def populate_sample_data():
    """Populate database with sample games and reviews"""
    conn = get_db_connection()
    
    # Check if data already exists
    existing_games = conn.execute("SELECT COUNT(*) as count FROM games").fetchone()
    if existing_games['count'] > 0:
        print("Sample data already exists, skipping population...")
        conn.close()
        return
    
    # Sample games data (50+ popular games)
    games_data = [
        # Action/Adventure
        ("God of War", "<PERSON> and his son <PERSON><PERSON> on their journey through Norse mythology.", "Action/Adventure", "PC,PlayStation 4,PlayStation 5", "god_of_war.jpg"),
        ("The Last of Us Part II", "A post-apocalyptic survival story about <PERSON>'s journey for revenge.", "Action/Adventure", "PlayStation 4,PlayStation 5", "tlou2.jpg"),
        ("Red Dead Redemption 2", "An epic tale of life in America's unforgiving heartland.", "Action/Adventure", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "rdr2.jpg"),
        ("Grand Theft Auto V", "Experience the intertwining stories of three unique criminals.", "Action/Adventure", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "gtav.jpg"),
        ("Cyberpunk 2077", "An open-world action-adventure story set in Night City.", "Action/Adventure", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "cyberpunk2077.jpg"),
        ("Assassin's Creed Valhalla", "Become Eivor, a legendary Viking raider on a quest for glory.", "Action/Adventure", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "ac_valhalla.jpg"),
        ("Spider-Man: Miles Morales", "Experience the rise of Miles Morales as the new Spider-Man.", "Action/Adventure", "PlayStation 4,PlayStation 5,PC", "spiderman_miles.jpg"),
        ("Horizon Zero Dawn", "Hunt robotic creatures in a post-apocalyptic world.", "Action/Adventure", "PC,PlayStation 4,PlayStation 5", "horizon_zero_dawn.jpg"),
        ("Uncharted 4: A Thief's End", "Nathan Drake's final adventure in search of pirate treasure.", "Action/Adventure", "PlayStation 4,PlayStation 5", "uncharted4.jpg"),
        ("Ghost of Tsushima", "Forge a new path and wage an unconventional war for freedom.", "Action/Adventure", "PlayStation 4,PlayStation 5,PC", "ghost_of_tsushima.jpg"),
        
        # RPG
        ("The Witcher 3: Wild Hunt", "Hunt monsters and explore a vast open world as Geralt of Rivia.", "RPG", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "witcher3.jpg"),
        ("Elden Ring", "A fantasy action-RPG adventure set in the Lands Between.", "RPG", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "elden_ring.jpg"),
        ("Final Fantasy VII Remake", "A reimagining of the iconic JRPG with modern gameplay.", "RPG", "PlayStation 4,PlayStation 5,PC", "ff7_remake.jpg"),
        ("Persona 5 Royal", "Live the life of a high school student with supernatural powers.", "RPG", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "persona5_royal.jpg"),
        ("Mass Effect Legendary Edition", "The complete Mass Effect trilogy remastered.", "RPG", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "mass_effect_legendary.jpg"),
        ("Baldur's Gate 3", "A next-generation RPG set in the world of Dungeons & Dragons.", "RPG", "PC,PlayStation 5,Xbox Series X/S", "baldurs_gate3.jpg"),
        ("Skyrim Special Edition", "The legendary open-world RPG, now enhanced.", "RPG", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "skyrim_se.jpg"),
        ("Divinity: Original Sin 2", "A tactical RPG with deep character customization.", "RPG", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "divinity_os2.jpg"),
        
        # Shooter
        ("Call of Duty: Modern Warfare II", "The ultimate weapon is team in this multiplayer combat experience.", "Shooter", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "cod_mw2.jpg"),
        ("Apex Legends", "Choose from a diverse cast of Legends and fight for glory.", "Battle Royale", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "apex_legends.jpg"),
        ("Valorant", "A 5v5 character-based tactical shooter.", "Shooter", "PC", "valorant.jpg"),
        ("Overwatch 2", "The world needs heroes in this team-based shooter.", "Shooter", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "overwatch2.jpg"),
        ("Destiny 2", "Become a Guardian and protect the last safe city on Earth.", "Shooter", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "destiny2.jpg"),
        ("Counter-Strike 2", "The legendary tactical shooter, reimagined.", "Shooter", "PC", "cs2.jpg"),
        
        # Sports
        ("FIFA 24", "The world's game with HyperMotionV technology.", "Sports", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "fifa24.jpg"),
        ("NBA 2K24", "Rise to the occasion in the most authentic NBA experience.", "Sports", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "nba2k24.jpg"),
        ("Madden NFL 24", "Win with strategy in the most realistic NFL experience.", "Sports", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "madden24.jpg"),
        ("F1 23", "Be the last to brake in the official game of Formula 1.", "Racing", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "f1_23.jpg"),
        
        # Strategy
        ("Civilization VI", "Build an empire to stand the test of time.", "Strategy", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "civ6.jpg"),
        ("Age of Empires IV", "One of the most beloved real-time strategy games returns.", "Strategy", "PC,Xbox One,Xbox Series X/S", "aoe4.jpg"),
        ("Total War: Warhammer III", "The cataclysmic conclusion to the Total War: Warhammer trilogy.", "Strategy", "PC", "tw_warhammer3.jpg"),
        
        # Nintendo Exclusives
        ("The Legend of Zelda: Breath of the Wild", "Enter a world of discovery, exploration, and adventure.", "Adventure", "Nintendo Switch", "zelda_botw.jpg"),
        ("Super Mario Odyssey", "Join Mario on a massive, globe-trotting 3D adventure.", "Platformer", "Nintendo Switch", "mario_odyssey.jpg"),
        ("Animal Crossing: New Horizons", "Create your perfect island getaway.", "Simulation", "Nintendo Switch", "animal_crossing_nh.jpg"),
        ("Mario Kart 8 Deluxe", "Hit the road with the definitive version of Mario Kart 8.", "Racing", "Nintendo Switch", "mario_kart8_deluxe.jpg"),
        ("Super Smash Bros. Ultimate", "The biggest crossover in gaming history.", "Fighting", "Nintendo Switch", "smash_ultimate.jpg"),
        ("Pokémon Scarlet and Violet", "Catch, battle, and train Pokémon in the Paldea region.", "RPG", "Nintendo Switch", "pokemon_sv.jpg"),
        
        # Indie Games
        ("Hades", "Defy the god of the dead in this rogue-like dungeon crawler.", "Indie", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "hades.jpg"),
        ("Celeste", "Help Madeline survive her inner demons on her journey to the top.", "Indie", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "celeste.jpg"),
        ("Hollow Knight", "Forge your own path in this atmospheric Metroidvania.", "Indie", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "hollow_knight.jpg"),
        ("Stardew Valley", "Build the farm of your dreams in this charming countryside RPG.", "Indie", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "stardew_valley.jpg"),
        ("Among Us", "Play online or over local WiFi with 4-15 players.", "Indie", "PC,iOS,Android,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "among_us.jpg"),
        ("Fall Guys", "Dive into a series of ridiculous challenges and wild obstacle courses.", "Indie", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "fall_guys.jpg"),
        
        # Mobile Games
        ("Genshin Impact", "Step into Teyvat, a vast world teeming with life.", "RPG", "PC,PlayStation 4,PlayStation 5,iOS,Android", "genshin_impact.jpg"),
        ("PUBG Mobile", "The original battle royale experience on mobile.", "Battle Royale", "iOS,Android", "pubg_mobile.jpg"),
        ("Call of Duty: Mobile", "The definitive mobile FPS experience.", "Shooter", "iOS,Android", "cod_mobile.jpg"),
        ("Clash Royale", "Enter the Arena! Build your Battle Deck and outsmart the enemy.", "Strategy", "iOS,Android", "clash_royale.jpg"),
        ("Minecraft", "Build, explore, and survive in infinite worlds.", "Sandbox", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch,iOS,Android", "minecraft.jpg"),
        
        # Horror
        ("Resident Evil 4", "Survival horror returns with a vengeance.", "Horror", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "re4_remake.jpg"),
        ("Dead Space", "The sci-fi survival horror classic returns.", "Horror", "PC,PlayStation 5,Xbox Series X/S", "dead_space_remake.jpg"),
        ("Phasmophobia", "Paranormal investigation game for up to 4 players.", "Horror", "PC", "phasmophobia.jpg"),
        
        # Simulation
        ("Microsoft Flight Simulator", "Take to the skies and explore the world in stunning detail.", "Simulation", "PC,Xbox One,Xbox Series X/S", "msfs.jpg"),
        ("Cities: Skylines", "Build the city of your dreams.", "Simulation", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S,Nintendo Switch", "cities_skylines.jpg"),
        ("The Sims 4", "Create unique Sims and build their perfect homes.", "Simulation", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "sims4.jpg"),
        
        # Fighting
        ("Street Fighter 6", "The legendary fighting franchise returns.", "Fighting", "PC,PlayStation 4,PlayStation 5,Xbox One,Xbox Series X/S", "sf6.jpg"),
        ("Tekken 8", "Fist meets fate in the ultimate fighting experience.", "Fighting", "PC,PlayStation 5,Xbox Series X/S", "tekken8.jpg"),
        ("Mortal Kombat 1", "A new era of Mortal Kombat begins.", "Fighting", "PC,PlayStation 5,Xbox Series X/S,Nintendo Switch", "mk1.jpg")
    ]

    # Insert games into database
    print("Populating games...")
    for game_data in games_data:
        conn.execute(
            """INSERT INTO games (title, description, genre, platforms, cover_image)
               VALUES (?, ?, ?, ?, ?)""",
            game_data
        )

    # Sample reviews data
    sample_reviews = [
        # God of War reviews
        (1, "GameMaster", 5, "Absolutely incredible game! The relationship between Kratos and Atreus is beautifully developed. Combat feels weighty and satisfying.", "PlayStation 5", "Amazing story|Great combat|Beautiful graphics", "Can be slow at times", 15, 120),
        (1, "NorseMyth", 4, "Great game overall, though I wish there were more boss fights. The world building is fantastic.", "PC", "Excellent world building|Good character development", "Limited boss variety", 8, 95),
        (1, "AxeThrow", 5, "Best game I've played in years. Every aspect is polished to perfection.", "PlayStation 4", "Perfect gameplay|Stunning visuals|Emotional story", "", 22, 180),

        # The Last of Us Part II reviews
        (2, "ZombieSlayer", 4, "Controversial story choices but undeniably well-crafted. Graphics are phenomenal.", "PlayStation 5", "Incredible graphics|Solid gameplay|Great voice acting", "Divisive story", 12, 87),
        (2, "PostApoc", 3, "Good game but didn't live up to the first one for me. Still worth playing.", "PlayStation 4", "Good gameplay mechanics|Nice graphics", "Story felt forced|Too long", 5, 65),

        # Elden Ring reviews
        (12, "SoulsBorne", 5, "FromSoftware's masterpiece. Open world done right with challenging but fair combat.", "PC", "Perfect difficulty|Amazing world design|Great exploration", "Can be overwhelming for newcomers", 45, 250),
        (12, "TarnishedOne", 5, "Game of the year material. Every discovery feels rewarding.", "PlayStation 5", "Incredible exploration|Fantastic boss fights|Beautiful art", "", 38, 195),
        (12, "RingBearer", 4, "Challenging but rewarding. The open world adds so much to the Souls formula.", "Xbox Series X/S", "Great open world|Excellent combat|Good story", "Very difficult", 19, 142),

        # Minecraft reviews
        (47, "BlockBuilder", 5, "Timeless classic. Creativity has no limits in this game.", "PC", "Infinite creativity|Great multiplayer|Regular updates", "Graphics might not appeal to everyone", 67, 420),
        (47, "CraftMaster", 5, "Perfect game for all ages. Building and exploring never gets old.", "Nintendo Switch", "Family friendly|Endless possibilities|Cross-platform play", "", 34, 280),

        # Apex Legends reviews
        (20, "BattleRoyaler", 4, "Best BR game in my opinion. Character abilities add great tactical depth.", "PC", "Great character abilities|Smooth gameplay|Good communication system", "Can be frustrating with random teammates", 28, 165),
        (20, "LegendHunter", 4, "Fast-paced and fun. Regular updates keep it fresh.", "PlayStation 5", "Fast gameplay|Regular content updates|Good gunplay", "Steep learning curve", 15, 98),

        # Among Us reviews
        (41, "Impostor", 4, "Simple but addictive. Great party game with friends.", "iOS", "Fun with friends|Simple mechanics|Cross-platform", "Gets repetitive|Needs more content", 89, 340),
        (41, "CrewMate", 3, "Fun for a while but lacks depth for long-term play.", "Android", "Easy to learn|Good for groups", "Limited content|Can get boring", 12, 78),

        # Genshin Impact reviews
        (42, "Traveler", 4, "Beautiful open world with engaging combat. Gacha system can be predatory though.", "PC", "Stunning visuals|Great exploration|Free to play", "Gacha mechanics|Grinding required", 23, 156),
        (42, "Paimon", 5, "Amazing game for free! So much content and beautiful world to explore.", "iOS", "Free to play|Beautiful graphics|Lots of content", "Battery drain on mobile", 41, 287),

        # Hades reviews
        (37, "IndieGamer", 5, "Perfect roguelike. Every run feels different and the story is brilliantly told.", "Nintendo Switch", "Perfect gameplay loop|Great story|Excellent voice acting", "", 52, 312),
        (37, "UnderWorld", 5, "Best indie game ever made. Supergiant Games outdid themselves.", "PC", "Amazing art style|Perfect difficulty curve|Great music", "", 67, 398),

        # Stardew Valley reviews
        (40, "Farmer", 5, "So relaxing and addictive. Perfect game to unwind after a long day.", "PC", "Very relaxing|Addictive gameplay|Great pixel art", "Can be overwhelming at first", 78, 445),
        (40, "ValleyLife", 4, "Charming farming sim with surprising depth. Great for stress relief.", "Nintendo Switch", "Relaxing gameplay|Lots of activities|Good story", "Slow start", 34, 201)
    ]

    # Insert sample reviews
    print("Populating sample reviews...")
    for review_data in sample_reviews:
        # Create review with random timestamp in the last 30 days
        days_ago = random.randint(1, 30)
        created_at = datetime.now() - timedelta(days=days_ago)

        conn.execute(
            """INSERT INTO reviews
               (game_id, reviewer_name, rating, review_text, platform, pros, cons, likes_count, views_count, created_at)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
            review_data + (created_at,)
        )

    # Update game ratings based on reviews
    print("Updating game ratings...")
    games = conn.execute("SELECT id FROM games").fetchall()
    for game in games:
        game_id = game['id']

        # Calculate average rating and total reviews
        result = conn.execute(
            """SELECT AVG(rating) as avg_rating, COUNT(*) as total_reviews
               FROM reviews WHERE game_id = ?""",
            (game_id,)
        ).fetchone()

        avg_rating = result['avg_rating'] if result['avg_rating'] else 0.0
        total_reviews = result['total_reviews']

        # Update game record
        conn.execute(
            """UPDATE games
               SET average_rating = ?, total_reviews = ?
               WHERE id = ?""",
            (round(avg_rating, 1), total_reviews, game_id)
        )

    conn.commit()
    conn.close()

    print(f"Sample data populated successfully!")
    print(f"- {len(games_data)} games added")
    print(f"- {len(sample_reviews)} reviews added")
    print("Database ready for testing!")

if __name__ == "__main__":
    populate_sample_data()

"""
Sample Data Loader for GameReview Pro Python Application
Loads comprehensive sample data for testing and demonstration
"""

import json
import random
from datetime import datetime, timedelta
from typing import List, Dict
import logging

from ..database.models import db_manager, Game, Review, User

def load_sample_data():
    """Load comprehensive sample data"""
    session = db_manager.get_session()
    
    try:
        # Check if data already exists
        existing_games = session.query(Game).count()
        if existing_games > 0:
            print("✅ Sample data already exists, skipping...")
            return
        
        print("📊 Loading sample data...")
        
        # Load sample games
        games_data = get_sample_games()
        game_objects = []
        
        for game_data in games_data:
            game = Game(**game_data)
            session.add(game)
            game_objects.append(game)
        
        session.commit()
        print(f"✅ Loaded {len(games_data)} sample games")
        
        # Load sample reviews
        reviews_data = get_sample_reviews(game_objects)
        
        for review_data in reviews_data:
            review = Review(**review_data)
            session.add(review)
        
        session.commit()
        print(f"✅ Loaded {len(reviews_data)} sample reviews")
        
        # Update game statistics
        update_game_statistics(session, game_objects)
        
        print("✅ Sample data loaded successfully!")
        
    except Exception as e:
        session.rollback()
        logging.error(f"Error loading sample data: {e}")
        print(f"❌ Error loading sample data: {e}")
    finally:
        session.close()

def get_sample_games() -> List[Dict]:
    """Get comprehensive sample games data"""
    return [
        # Action/Adventure Games
        {
            'title': 'God of War',
            'description': 'Follow Kratos and his son Atreus on their journey through Norse mythology in this epic action-adventure.',
            'genre': 'Action/Adventure',
            'platforms': '["PC", "PlayStation 4", "PlayStation 5"]',
            'developer': 'Santa Monica Studio',
            'publisher': 'Sony Interactive Entertainment',
            'release_date': '2018-04-20',
            'cover_image': 'https://image.api.playstation.com/vulcan/img/rnd/202010/2217/LsaRVLF8vBZ9ouWtysaFrSnt.png',
            'metacritic_score': 94,
            'price': 39.99
        },
        {
            'title': 'The Last of Us Part II',
            'description': 'A post-apocalyptic survival story about Ellie\'s journey for revenge in a world overrun by infected.',
            'genre': 'Action/Adventure',
            'platforms': '["PlayStation 4", "PlayStation 5"]',
            'developer': 'Naughty Dog',
            'publisher': 'Sony Interactive Entertainment',
            'release_date': '2020-06-19',
            'cover_image': 'https://image.api.playstation.com/vulcan/img/rnd/202010/2618/Y9jbVLPAjqXzwav3HXd9lvbM.png',
            'metacritic_score': 93,
            'price': 29.99
        },
        {
            'title': 'Red Dead Redemption 2',
            'description': 'An epic tale of life in America\'s unforgiving heartland, featuring Arthur Morgan and the Van der Linde gang.',
            'genre': 'Action/Adventure',
            'platforms': '["PC", "PlayStation 4", "PlayStation 5", "Xbox One", "Xbox Series X/S"]',
            'developer': 'Rockstar Games',
            'publisher': 'Rockstar Games',
            'release_date': '2018-10-26',
            'cover_image': 'https://media.rockstargames.com/rockstargames/img/global/news/upload/actual_1364906194.jpg',
            'metacritic_score': 97,
            'price': 59.99
        },
        
        # RPG Games
        {
            'title': 'The Witcher 3: Wild Hunt',
            'description': 'Hunt monsters and explore a vast open world as Geralt of Rivia in this critically acclaimed RPG.',
            'genre': 'RPG',
            'platforms': '["PC", "PlayStation 4", "PlayStation 5", "Xbox One", "Xbox Series X/S", "Nintendo Switch"]',
            'developer': 'CD Projekt Red',
            'publisher': 'CD Projekt',
            'release_date': '2015-05-19',
            'cover_image': 'https://image.api.playstation.com/vulcan/img/rnd/202211/0711/kh4MUIuMmHlktOHar3lVl6rY.png',
            'metacritic_score': 93,
            'price': 39.99
        },
        {
            'title': 'Elden Ring',
            'description': 'A fantasy action-RPG adventure set in the Lands Between, created by FromSoftware and George R.R. Martin.',
            'genre': 'RPG',
            'platforms': '["PC", "PlayStation 4", "PlayStation 5", "Xbox One", "Xbox Series X/S"]',
            'developer': 'FromSoftware',
            'publisher': 'Bandai Namco Entertainment',
            'release_date': '2022-02-25',
            'cover_image': 'https://image.api.playstation.com/vulcan/ap/rnd/202110/2000/phvVT0qZfcRms5qDAk0SI3CM.png',
            'metacritic_score': 96,
            'price': 59.99
        },
        {
            'title': 'Baldur\'s Gate 3',
            'description': 'A next-generation RPG set in the world of Dungeons & Dragons with unprecedented freedom and choice.',
            'genre': 'RPG',
            'platforms': '["PC", "PlayStation 5", "Xbox Series X/S"]',
            'developer': 'Larian Studios',
            'publisher': 'Larian Studios',
            'release_date': '2023-08-03',
            'cover_image': 'https://shared.akamai.steamstatic.com/store_item_assets/steam/apps/1086940/header.jpg',
            'metacritic_score': 96,
            'price': 59.99
        },
        
        # Shooter Games
        {
            'title': 'Call of Duty: Modern Warfare II',
            'description': 'The ultimate weapon is team in this multiplayer combat experience featuring Task Force 141.',
            'genre': 'Shooter',
            'platforms': '["PC", "PlayStation 4", "PlayStation 5", "Xbox One", "Xbox Series X/S"]',
            'developer': 'Infinity Ward',
            'publisher': 'Activision',
            'release_date': '2022-10-28',
            'cover_image': 'https://www.callofduty.com/content/dam/atvi/callofduty/cod-touchui/mw2/meta-images/reveal/mw2-reveal-meta.jpg',
            'metacritic_score': 75,
            'price': 69.99
        },
        {
            'title': 'Valorant',
            'description': 'A 5v5 character-based tactical shooter where precise gunplay meets unique agent abilities.',
            'genre': 'Shooter',
            'platforms': '["PC"]',
            'developer': 'Riot Games',
            'publisher': 'Riot Games',
            'release_date': '2020-06-02',
            'cover_image': 'https://images.contentstack.io/v3/assets/bltb6530b271fddd0b1/blt5648d0b0b5c8e2b8/5eb7cdc1b1eaac3a5b2e5c5c/valorant-logo.jpg',
            'metacritic_score': 80,
            'price': 0.00  # Free to play
        },
        
        # Strategy Games
        {
            'title': 'Civilization VI',
            'description': 'Build an empire to stand the test of time in this turn-based strategy masterpiece.',
            'genre': 'Strategy',
            'platforms': '["PC", "PlayStation 4", "PlayStation 5", "Xbox One", "Xbox Series X/S", "Nintendo Switch"]',
            'developer': '2K Games',
            'publisher': '2K Games',
            'release_date': '2016-10-21',
            'cover_image': 'https://cdn.2k.com/locale/en-US/civ6/civ6-logo.png',
            'metacritic_score': 88,
            'price': 59.99
        },
        
        # Indie Games
        {
            'title': 'Hades',
            'description': 'Defy the god of the dead in this rogue-like dungeon crawler from the creators of Bastion and Transistor.',
            'genre': 'Indie',
            'platforms': '["PC", "PlayStation 4", "PlayStation 5", "Xbox One", "Xbox Series X/S", "Nintendo Switch"]',
            'developer': 'Supergiant Games',
            'publisher': 'Supergiant Games',
            'release_date': '2020-09-17',
            'cover_image': 'https://cdn.cloudflare.steamstatic.com/steam/apps/1145360/header.jpg',
            'metacritic_score': 93,
            'price': 24.99
        },
        {
            'title': 'Stardew Valley',
            'description': 'Build the farm of your dreams in this charming countryside RPG with endless possibilities.',
            'genre': 'Indie',
            'platforms': '["PC", "PlayStation 4", "PlayStation 5", "Xbox One", "Xbox Series X/S", "Nintendo Switch", "iOS", "Android"]',
            'developer': 'ConcernedApe',
            'publisher': 'ConcernedApe',
            'release_date': '2016-02-26',
            'cover_image': 'https://cdn.cloudflare.steamstatic.com/steam/apps/413150/header.jpg',
            'metacritic_score': 89,
            'price': 14.99
        },
        
        # Horror Games
        {
            'title': 'Resident Evil 4',
            'description': 'Survival horror returns with a vengeance in this remake of the beloved classic.',
            'genre': 'Horror',
            'platforms': '["PC", "PlayStation 4", "PlayStation 5", "Xbox One", "Xbox Series X/S"]',
            'developer': 'Capcom',
            'publisher': 'Capcom',
            'release_date': '2023-03-24',
            'cover_image': 'https://image.api.playstation.com/vulcan/ap/rnd/202210/0706/EVWyZD63pahuh95eKloFaJuC.png',
            'metacritic_score': 90,
            'price': 59.99
        },
        
        # Sports Games
        {
            'title': 'FIFA 24',
            'description': 'The world\'s game with HyperMotionV technology and the most authentic football experience.',
            'genre': 'Sports',
            'platforms': '["PC", "PlayStation 4", "PlayStation 5", "Xbox One", "Xbox Series X/S", "Nintendo Switch"]',
            'developer': 'EA Sports',
            'publisher': 'Electronic Arts',
            'release_date': '2023-09-29',
            'cover_image': 'https://media.contentapi.ea.com/content/dam/ea/fifa/fifa-24/common/f24-standard-edition-keyart.jpg',
            'metacritic_score': 78,
            'price': 69.99
        },
        
        # Mobile Games
        {
            'title': 'Genshin Impact',
            'description': 'Step into Teyvat, a vast world teeming with life and flowing with elemental energy.',
            'genre': 'RPG',
            'platforms': '["PC", "PlayStation 4", "PlayStation 5", "iOS", "Android"]',
            'developer': 'miHoYo',
            'publisher': 'miHoYo',
            'release_date': '2020-09-28',
            'cover_image': 'https://webstatic.mihoyo.com/upload/event/2021/02/25/c5b4c0b7e7b8b7b8b7b8b7b8b7b8b7b8.jpg',
            'metacritic_score': 84,
            'price': 0.00  # Free to play
        },
        
        # Nintendo Exclusives
        {
            'title': 'The Legend of Zelda: Breath of the Wild',
            'description': 'Enter a world of discovery, exploration, and adventure in this open-world Zelda adventure.',
            'genre': 'Adventure',
            'platforms': '["Nintendo Switch"]',
            'developer': 'Nintendo',
            'publisher': 'Nintendo',
            'release_date': '2017-03-03',
            'cover_image': 'https://assets.nintendo.com/image/upload/c_fill,w_1200/q_auto:best/f_auto/dpr_2.0/ncom/en_US/games/switch/t/the-legend-of-zelda-breath-of-the-wild-switch/hero',
            'metacritic_score': 97,
            'price': 59.99
        }
    ]

def get_sample_reviews(games: List[Game]) -> List[Dict]:
    """Generate sample reviews for games"""
    reviews = []
    
    # Sample review templates
    review_templates = [
        {
            'rating': 5,
            'text': 'Absolutely incredible game! The story is captivating and the gameplay is smooth and engaging. Every aspect is polished to perfection.',
            'pros': ['Amazing story', 'Great gameplay', 'Beautiful graphics', 'Excellent sound design'],
            'cons': []
        },
        {
            'rating': 4,
            'text': 'Really solid game overall. Great mechanics and interesting world, though it has a few minor issues that prevent it from being perfect.',
            'pros': ['Good gameplay', 'Interesting world', 'Nice graphics'],
            'cons': ['Some minor bugs', 'Could use more content']
        },
        {
            'rating': 3,
            'text': 'Decent game but nothing special. It\'s enjoyable enough but doesn\'t really stand out from other games in the genre.',
            'pros': ['Okay gameplay', 'Decent graphics'],
            'cons': ['Generic story', 'Repetitive gameplay', 'Not very innovative']
        },
        {
            'rating': 2,
            'text': 'Disappointing experience. The game has potential but is held back by numerous issues and poor execution.',
            'pros': ['Good concept'],
            'cons': ['Poor execution', 'Many bugs', 'Boring gameplay', 'Weak story']
        }
    ]
    
    platforms = ['PC', 'PlayStation 5', 'PlayStation 4', 'Xbox Series X/S', 'Xbox One', 'Nintendo Switch']
    reviewer_names = [
        'GameMaster2023', 'ProGamer', 'CasualPlayer', 'HardcoreGamer', 'IndieExplorer',
        'RPGLover', 'ActionFan', 'StrategyExpert', 'HorrorEnthusiast', 'SportsGamer',
        'RetroGamer', 'NextGenPlayer', 'GameCritic', 'PlayerOne', 'GamerGirl',
        'ConsoleWarrior', 'PCMasterRace', 'MobileGamer', 'StreamerPro', 'EsportsPlayer'
    ]
    
    # Generate reviews for each game
    for game in games:
        num_reviews = random.randint(2, 8)  # 2-8 reviews per game
        
        for _ in range(num_reviews):
            template = random.choice(review_templates)
            
            # Add some variation to the template
            rating = template['rating']
            if random.random() < 0.3:  # 30% chance to vary rating by 1
                rating = max(1, min(5, rating + random.choice([-1, 1])))
            
            # Random creation date (last 6 months)
            days_ago = random.randint(1, 180)
            created_at = datetime.utcnow() - timedelta(days=days_ago)
            
            review_data = {
                'game_id': game.id,
                'reviewer_name': random.choice(reviewer_names),
                'rating': rating,
                'review_text': template['text'],
                'platform': random.choice(platforms),
                'hours_played': round(random.uniform(5.0, 200.0), 1),
                'pros': json.dumps(template['pros']),
                'cons': json.dumps(template['cons']),
                'likes_count': random.randint(0, 50),
                'views_count': random.randint(10, 500),
                'created_at': created_at
            }
            
            reviews.append(review_data)
    
    return reviews

def update_game_statistics(session, games: List[Game]):
    """Update game statistics based on reviews"""
    for game in games:
        reviews = session.query(Review).filter(Review.game_id == game.id).all()
        
        if reviews:
            ratings = [review.rating for review in reviews]
            average_rating = sum(ratings) / len(ratings)
            total_reviews = len(reviews)
            
            # Calculate popularity score
            popularity_score = (average_rating / 5.0) * 0.7 + min(total_reviews / 50.0, 1.0) * 0.3
            
            game.average_rating = round(average_rating, 1)
            game.total_reviews = total_reviews
            game.popularity_score = round(popularity_score, 2)
    
    session.commit()
    print("✅ Updated game statistics")

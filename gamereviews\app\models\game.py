"""
Game model for GameReview Application
"""

from datetime import datetime
from typing import List, Dict, Optional
from ..database import execute_query, execute_insert, execute_update

class Game:
    """Game model for managing game data"""
    
    def __init__(self, id=None, title=None, description=None, genre=None, 
                 platforms=None, cover_image=None, average_rating=0.0, 
                 total_reviews=0, created_at=None):
        self.id = id
        self.title = title
        self.description = description
        self.genre = genre
        self.platforms = platforms  # JSON string of platforms
        self.cover_image = cover_image
        self.average_rating = average_rating
        self.total_reviews = total_reviews
        self.created_at = created_at or datetime.now()
    
    def to_dict(self) -> Dict:
        """Convert game object to dictionary"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'genre': self.genre,
            'platforms': self.platforms.split(',') if self.platforms else [],
            'cover_image': self.cover_image,
            'average_rating': self.average_rating,
            'total_reviews': self.total_reviews,
            'created_at': self.created_at.isoformat() if isinstance(self.created_at, datetime) else self.created_at
        }
    
    @staticmethod
    def from_row(row) -> 'Game':
        """Create Game object from database row"""
        return Game(
            id=row['id'],
            title=row['title'],
            description=row['description'],
            genre=row['genre'],
            platforms=row['platforms'],
            cover_image=row['cover_image'],
            average_rating=row['average_rating'],
            total_reviews=row['total_reviews'],
            created_at=row['created_at']
        )
    
    @staticmethod
    def get_all(page=1, limit=20, genre=None, platform=None, sort_by='title'):
        """Get all games with optional filtering and pagination"""
        offset = (page - 1) * limit
        
        # Build query
        query = "SELECT * FROM games WHERE 1=1"
        params = []
        
        if genre:
            query += " AND genre LIKE ?"
            params.append(f"%{genre}%")
        
        if platform:
            query += " AND platforms LIKE ?"
            params.append(f"%{platform}%")
        
        # Add sorting
        if sort_by == 'rating':
            query += " ORDER BY average_rating DESC"
        elif sort_by == 'reviews':
            query += " ORDER BY total_reviews DESC"
        else:
            query += " ORDER BY title ASC"
        
        query += " LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        rows = execute_query(query, tuple(params))
        games = [Game.from_row(row) for row in rows]
        
        # Get total count for pagination
        count_query = "SELECT COUNT(*) as total FROM games WHERE 1=1"
        count_params = []
        
        if genre:
            count_query += " AND genre LIKE ?"
            count_params.append(f"%{genre}%")
        
        if platform:
            count_query += " AND platforms LIKE ?"
            count_params.append(f"%{platform}%")
        
        total_result = execute_query(count_query, tuple(count_params))
        total_games = total_result[0]['total'] if total_result else 0
        
        return games, total_games
    
    @staticmethod
    def get_by_id(game_id: int) -> Optional['Game']:
        """Get game by ID"""
        rows = execute_query("SELECT * FROM games WHERE id = ?", (game_id,))
        return Game.from_row(rows[0]) if rows else None
    
    @staticmethod
    def search(query: str) -> List['Game']:
        """Search games by title"""
        rows = execute_query(
            "SELECT * FROM games WHERE title LIKE ? ORDER BY title ASC",
            (f"%{query}%",)
        )
        return [Game.from_row(row) for row in rows]
    
    @staticmethod
    def get_top_rated(limit: int = 10) -> List['Game']:
        """Get top rated games"""
        rows = execute_query(
            """SELECT * FROM games 
               WHERE total_reviews > 0 
               ORDER BY average_rating DESC, total_reviews DESC 
               LIMIT ?""",
            (limit,)
        )
        return [Game.from_row(row) for row in rows]
    
    def save(self) -> int:
        """Save game to database"""
        if self.id:
            # Update existing game
            execute_update(
                """UPDATE games 
                   SET title=?, description=?, genre=?, platforms=?, cover_image=?
                   WHERE id=?""",
                (self.title, self.description, self.genre, self.platforms, 
                 self.cover_image, self.id)
            )
            return self.id
        else:
            # Insert new game
            game_id = execute_insert(
                """INSERT INTO games (title, description, genre, platforms, cover_image)
                   VALUES (?, ?, ?, ?, ?)""",
                (self.title, self.description, self.genre, self.platforms, self.cover_image)
            )
            self.id = game_id
            return game_id

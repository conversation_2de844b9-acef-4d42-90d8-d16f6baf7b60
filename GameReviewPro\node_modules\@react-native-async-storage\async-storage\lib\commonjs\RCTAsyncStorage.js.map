{"version": 3, "names": ["_reactNative", "require", "_shouldFallbackToLegacyNativeModule", "RCTAsyncStorage", "TurboModuleRegistry", "get", "NativeModules", "shouldFallbackToLegacyNativeModule", "_default", "exports", "default"], "sources": ["RCTAsyncStorage.ts"], "sourcesContent": ["import { NativeModules, TurboModuleRegistry } from \"react-native\";\nimport { shouldFallbackToLegacyNativeModule } from \"./shouldFallbackToLegacyNativeModule\";\n\n// TurboModuleRegistry falls back to NativeModules so we don't have to try go\n// assign NativeModules' counterparts if TurboModuleRegistry would resolve\n// with undefined.\nlet RCTAsyncStorage = TurboModuleRegistry\n  ? TurboModuleRegistry.get(\"PlatformLocalStorage\") || // Support for external modules, like react-native-windows\n    TurboModuleRegistry.get(\"RNC_AsyncSQLiteDBStorage\") ||\n    TurboModuleRegistry.get(\"RNCAsyncStorage\")\n  : NativeModules[\"PlatformLocalStorage\"] || // Support for external modules, like react-native-windows\n    NativeModules[\"RNC_AsyncSQLiteDBStorage\"] ||\n    NativeModules[\"RNCAsyncStorage\"];\n\nif (!RCTAsyncStorage && shouldFallbackToLegacyNativeModule()) {\n  if (TurboModuleRegistry) {\n    RCTAsyncStorage =\n      TurboModuleRegistry.get(\"AsyncSQLiteDBStorage\") ||\n      TurboModuleRegistry.get(\"AsyncLocalStorage\");\n  } else {\n    RCTAsyncStorage =\n      NativeModules[\"AsyncSQLiteDBStorage\"] ||\n      NativeModules[\"AsyncLocalStorage\"];\n  }\n}\n\nexport default RCTAsyncStorage;\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,mCAAA,GAAAD,OAAA;AAEA;AACA;AACA;AACA,IAAIE,eAAe,GAAGC,gCAAmB,GACrCA,gCAAmB,CAACC,GAAG,CAAC,sBAAsB,CAAC;AAAI;AACnDD,gCAAmB,CAACC,GAAG,CAAC,0BAA0B,CAAC,IACnDD,gCAAmB,CAACC,GAAG,CAAC,iBAAiB,CAAC,GAC1CC,0BAAa,CAAC,sBAAsB,CAAC;AAAI;AACzCA,0BAAa,CAAC,0BAA0B,CAAC,IACzCA,0BAAa,CAAC,iBAAiB,CAAC;AAEpC,IAAI,CAACH,eAAe,IAAI,IAAAI,sEAAkC,EAAC,CAAC,EAAE;EAC5D,IAAIH,gCAAmB,EAAE;IACvBD,eAAe,GACbC,gCAAmB,CAACC,GAAG,CAAC,sBAAsB,CAAC,IAC/CD,gCAAmB,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAChD,CAAC,MAAM;IACLF,eAAe,GACbG,0BAAa,CAAC,sBAAsB,CAAC,IACrCA,0BAAa,CAAC,mBAAmB,CAAC;EACtC;AACF;AAAC,IAAAE,QAAA,GAEcL,eAAe;AAAAM,OAAA,CAAAC,OAAA,GAAAF,QAAA"}
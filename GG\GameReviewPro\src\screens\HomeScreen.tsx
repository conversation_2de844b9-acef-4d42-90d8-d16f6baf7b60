// Professional Home Screen
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Game, Review, NavigationProps, AppStats } from '../types';
import { colors, typography, layout, shadows } from '../theme';
import { apiService } from '../services/api';
import GameCard from '../components/GameCard';
import ReviewCard from '../components/ReviewCard';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const { width } = Dimensions.get('window');

export const HomeScreen: React.FC<NavigationProps> = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [featuredGames, setFeaturedGames] = useState<Game[]>([]);
  const [latestReviews, setLatestReviews] = useState<Review[]>([]);
  const [topRatedGames, setTopRatedGames] = useState<Game[]>([]);
  const [stats, setStats] = useState<AppStats | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    try {
      setError(null);
      
      // Load all data in parallel
      const [gamesResponse, reviewsResponse, topRatedResponse, statsResponse] = await Promise.all([
        apiService.getGames({ limit: 10, sort: 'reviews' }),
        apiService.getReviews({ limit: 5 }),
        apiService.getTopRatedGames(6),
        apiService.getStats(),
      ]);

      if (gamesResponse.success) {
        setFeaturedGames(gamesResponse.games);
      }

      if (reviewsResponse.success) {
        setLatestReviews(reviewsResponse.reviews);
      }

      if (topRatedResponse.success && topRatedResponse.data) {
        setTopRatedGames(topRatedResponse.data);
      }

      if (statsResponse.success && statsResponse.data) {
        setStats(statsResponse.data);
      }

    } catch (error) {
      console.error('Error loading home data:', error);
      setError('Failed to load data. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadHomeData();
  };

  const navigateToGame = (game: Game) => {
    navigation.navigate('GameDetail', { gameId: game.id });
  };

  const navigateToReview = (review: Review) => {
    navigation.navigate('ReviewDetail', { reviewId: review.id });
  };

  const navigateToDiscover = () => {
    navigation.navigate('Discover');
  };

  const navigateToWriteReview = () => {
    navigation.navigate('WriteReview');
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={loadHomeData} />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primaryBlue}
            colors={[colors.primaryBlue]}
          />
        }
      >
        {/* Header */}
        <LinearGradient
          colors={[colors.primaryBlue, colors.cyan]}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.welcomeText}>Welcome to</Text>
              <Text style={styles.appTitle}>GameReview Pro</Text>
            </View>
            <TouchableOpacity
              style={styles.searchButton}
              onPress={navigateToDiscover}
            >
              <Icon name="search" size={24} color={colors.white} />
            </TouchableOpacity>
          </View>

          {/* Stats */}
          {stats && (
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.total_games}</Text>
                <Text style={styles.statLabel}>Games</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.total_reviews}</Text>
                <Text style={styles.statLabel}>Reviews</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.overall_average_rating.toFixed(1)}</Text>
                <Text style={styles.statLabel}>Avg Rating</Text>
              </View>
            </View>
          )}
        </LinearGradient>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={navigateToWriteReview}
          >
            <LinearGradient
              colors={[colors.primaryBlue, colors.cyan]}
              style={styles.actionGradient}
            >
              <Icon name="rate-review" size={24} color={colors.white} />
              <Text style={styles.actionText}>Write Review</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={navigateToDiscover}
          >
            <View style={styles.actionSecondary}>
              <Icon name="explore" size={24} color={colors.primaryBlue} />
              <Text style={styles.actionTextSecondary}>Discover Games</Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Top Rated Games */}
        {topRatedGames.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Top Rated Games</Text>
              <TouchableOpacity onPress={navigateToDiscover}>
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>
            <FlatList
              data={topRatedGames}
              renderItem={({ item }) => (
                <GameCard
                  game={item}
                  onPress={navigateToGame}
                  style={styles.gameCard}
                />
              )}
              keyExtractor={(item) => item.id.toString()}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.horizontalList}
            />
          </View>
        )}

        {/* Latest Reviews */}
        {latestReviews.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Latest Reviews</Text>
              <TouchableOpacity onPress={() => navigation.navigate('Reviews')}>
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>
            {latestReviews.map((review) => (
              <ReviewCard
                key={review.id}
                review={review}
                onPress={navigateToReview}
                showGameInfo={true}
                style={styles.reviewCard}
              />
            ))}
          </View>
        )}

        {/* Featured Games */}
        {featuredGames.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Popular Games</Text>
              <TouchableOpacity onPress={navigateToDiscover}>
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.gamesGrid}>
              {featuredGames.slice(0, 6).map((game) => (
                <GameCard
                  key={game.id}
                  game={game}
                  onPress={navigateToGame}
                />
              ))}
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: layout.screenPadding,
    paddingTop: layout.lg,
    paddingBottom: layout.xl,
    borderBottomLeftRadius: layout.borderRadius * 2,
    borderBottomRightRadius: layout.borderRadius * 2,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: layout.lg,
  },
  welcomeText: {
    ...typography.body,
    color: colors.white,
    opacity: 0.8,
  },
  appTitle: {
    ...typography.h1,
    color: colors.white,
    fontWeight: '700',
  },
  searchButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: layout.borderRadius,
    paddingVertical: layout.md,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    ...typography.h2,
    color: colors.white,
    fontWeight: '700',
  },
  statLabel: {
    ...typography.caption,
    color: colors.white,
    opacity: 0.8,
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: layout.screenPadding,
    paddingVertical: layout.lg,
    gap: layout.md,
  },
  actionButton: {
    flex: 1,
    height: 60,
    borderRadius: layout.borderRadius,
    overflow: 'hidden',
  },
  actionGradient: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: layout.sm,
  },
  actionSecondary: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: layout.sm,
    backgroundColor: colors.darkGray,
    borderWidth: 1,
    borderColor: colors.primaryBlue,
  },
  actionText: {
    ...typography.button,
    color: colors.white,
  },
  actionTextSecondary: {
    ...typography.button,
    color: colors.primaryBlue,
  },
  section: {
    marginBottom: layout.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: layout.screenPadding,
    marginBottom: layout.md,
  },
  sectionTitle: {
    ...typography.h3,
    color: colors.white,
  },
  seeAllText: {
    ...typography.body,
    color: colors.primaryBlue,
    fontWeight: '500',
  },
  horizontalList: {
    paddingLeft: layout.screenPadding,
  },
  gameCard: {
    marginRight: layout.md,
  },
  reviewCard: {
    marginHorizontal: layout.screenPadding,
  },
  gamesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: layout.screenPadding,
  },
});

export default HomeScreen;

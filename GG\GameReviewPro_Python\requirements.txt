# GameReview Pro - Python Desktop Application
# Modern Python GUI with AI capabilities

# GUI Framework
tkinter-modern==1.0.0
customtkinter==5.2.0
pillow==10.0.1

# Database
sqlite3  # Built-in with Python
sqlalchemy==2.0.23

# AI and Machine Learning
torch==2.1.0
torchvision==0.16.0
transformers==4.35.0
scikit-learn==1.3.2
numpy==1.24.3
pandas==2.1.3

# Data Visualization
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Image Processing
opencv-python==********
Pillow==10.0.1

# Web Scraping (for game data)
requests==2.31.0
beautifulsoup4==4.12.2

# Natural Language Processing
nltk==3.8.1
textblob==0.17.1

# Data Analysis
scipy==1.11.4
statsmodels==0.14.0

# Utilities
python-dateutil==2.8.2
tqdm==4.66.1
colorama==0.4.6

# Optional: Web capabilities
flask==3.0.0
flask-cors==4.0.0

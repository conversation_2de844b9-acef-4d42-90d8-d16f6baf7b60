"""
Main GUI Window for GameReview Pro Python Application
Using CustomTkinter for modern, professional interface
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk
import threading
import json
from typing import Dict, List
import os
import sys

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from database.models import db_manager, Game, Review, User
from ai.sentiment_analyzer import sentiment_analyzer
from ai.recommendation_engine import recommendation_engine
from utils.data_manager import DataManager
from utils.image_processor import ImageProcessor

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")  # "dark" or "light"
ctk.set_default_color_theme("blue")  # "blue", "green", "dark-blue"

class GameReviewProApp:
    """Main application class"""
    
    def __init__(self):
        # Initialize main window
        self.root = ctk.CTk()
        self.root.title("GameReview Pro - AI-Powered Game Review Platform")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # Initialize components
        self.data_manager = DataManager()
        self.image_processor = ImageProcessor()
        self.current_user = None
        self.current_games = []
        self.current_reviews = []
        
        # Create GUI
        self.create_widgets()
        self.load_initial_data()
        
        print("✅ GameReview Pro GUI initialized successfully!")
    
    def create_widgets(self):
        """Create main GUI widgets"""
        # Configure grid
        self.root.grid_columnconfigure(1, weight=1)
        self.root.grid_rowconfigure(0, weight=1)
        
        # Create sidebar
        self.create_sidebar()
        
        # Create main content area
        self.create_main_content()
        
        # Create status bar
        self.create_status_bar()
    
    def create_sidebar(self):
        """Create navigation sidebar"""
        self.sidebar_frame = ctk.CTkFrame(self.root, width=250, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, rowspan=4, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(4, weight=1)
        
        # Logo/Title
        self.logo_label = ctk.CTkLabel(
            self.sidebar_frame, 
            text="🎮 GameReview Pro",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))
        
        # Navigation buttons
        self.nav_buttons = {}
        nav_items = [
            ("🏠 Dashboard", self.show_dashboard),
            ("🎮 Games", self.show_games),
            ("📝 Reviews", self.show_reviews),
            ("✍️ Write Review", self.show_write_review),
            ("🤖 AI Insights", self.show_ai_insights),
            ("📊 Analytics", self.show_analytics),
            ("⚙️ Settings", self.show_settings)
        ]
        
        for i, (text, command) in enumerate(nav_items, 1):
            btn = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                command=command,
                height=40,
                font=ctk.CTkFont(size=14)
            )
            btn.grid(row=i, column=0, padx=20, pady=10, sticky="ew")
            self.nav_buttons[text] = btn
        
        # User info section
        self.user_frame = ctk.CTkFrame(self.sidebar_frame)
        self.user_frame.grid(row=8, column=0, padx=20, pady=20, sticky="ew")
        
        self.user_label = ctk.CTkLabel(
            self.user_frame,
            text="👤 Guest User",
            font=ctk.CTkFont(size=12)
        )
        self.user_label.grid(row=0, column=0, padx=10, pady=10)
        
        # Theme toggle
        self.theme_switch = ctk.CTkSwitch(
            self.sidebar_frame,
            text="🌙 Dark Mode",
            command=self.toggle_theme
        )
        self.theme_switch.grid(row=9, column=0, padx=20, pady=10)
        self.theme_switch.select()  # Start in dark mode
    
    def create_main_content(self):
        """Create main content area"""
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.grid(row=0, column=1, sticky="nsew", padx=20, pady=20)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)
        
        # Create notebook for different views
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        
        # Initialize with dashboard
        self.show_dashboard()
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_frame = ctk.CTkFrame(self.root, height=30)
        self.status_frame.grid(row=1, column=1, sticky="ew", padx=20, pady=(0, 20))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Ready",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        # AI status indicator
        self.ai_status_label = ctk.CTkLabel(
            self.status_frame,
            text="🤖 AI Ready",
            font=ctk.CTkFont(size=12)
        )
        self.ai_status_label.grid(row=0, column=1, padx=10, pady=5, sticky="e")
    
    def show_dashboard(self):
        """Show dashboard view"""
        self.clear_main_content()
        
        # Dashboard frame
        dashboard_frame = ctk.CTkFrame(self.main_frame)
        dashboard_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        dashboard_frame.grid_columnconfigure((0, 1, 2), weight=1)
        
        # Welcome section
        welcome_label = ctk.CTkLabel(
            dashboard_frame,
            text="Welcome to GameReview Pro",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        welcome_label.grid(row=0, column=0, columnspan=3, padx=20, pady=20)
        
        # Stats cards
        self.create_stats_cards(dashboard_frame)
        
        # Recent activity
        self.create_recent_activity(dashboard_frame)
        
        # AI recommendations
        self.create_ai_recommendations(dashboard_frame)
        
        self.update_status("Dashboard loaded")
    
    def create_stats_cards(self, parent):
        """Create statistics cards"""
        stats = self.data_manager.get_app_statistics()
        
        # Total Games card
        games_card = ctk.CTkFrame(parent)
        games_card.grid(row=1, column=0, padx=10, pady=10, sticky="ew")
        
        ctk.CTkLabel(
            games_card,
            text="🎮",
            font=ctk.CTkFont(size=32)
        ).grid(row=0, column=0, padx=20, pady=10)
        
        ctk.CTkLabel(
            games_card,
            text=f"{stats.get('total_games', 0)}",
            font=ctk.CTkFont(size=24, weight="bold")
        ).grid(row=1, column=0, padx=20, pady=5)
        
        ctk.CTkLabel(
            games_card,
            text="Total Games",
            font=ctk.CTkFont(size=14)
        ).grid(row=2, column=0, padx=20, pady=(0, 15))
        
        # Total Reviews card
        reviews_card = ctk.CTkFrame(parent)
        reviews_card.grid(row=1, column=1, padx=10, pady=10, sticky="ew")
        
        ctk.CTkLabel(
            reviews_card,
            text="📝",
            font=ctk.CTkFont(size=32)
        ).grid(row=0, column=0, padx=20, pady=10)
        
        ctk.CTkLabel(
            reviews_card,
            text=f"{stats.get('total_reviews', 0)}",
            font=ctk.CTkFont(size=24, weight="bold")
        ).grid(row=1, column=0, padx=20, pady=5)
        
        ctk.CTkLabel(
            reviews_card,
            text="Total Reviews",
            font=ctk.CTkFont(size=14)
        ).grid(row=2, column=0, padx=20, pady=(0, 15))
        
        # Average Rating card
        avg_rating_card = ctk.CTkFrame(parent)
        avg_rating_card.grid(row=1, column=2, padx=10, pady=10, sticky="ew")
        
        ctk.CTkLabel(
            avg_rating_card,
            text="⭐",
            font=ctk.CTkFont(size=32)
        ).grid(row=0, column=0, padx=20, pady=10)
        
        ctk.CTkLabel(
            avg_rating_card,
            text=f"{stats.get('average_rating', 0.0):.1f}",
            font=ctk.CTkFont(size=24, weight="bold")
        ).grid(row=1, column=0, padx=20, pady=5)
        
        ctk.CTkLabel(
            avg_rating_card,
            text="Avg Rating",
            font=ctk.CTkFont(size=14)
        ).grid(row=2, column=0, padx=20, pady=(0, 15))
    
    def create_recent_activity(self, parent):
        """Create recent activity section"""
        activity_frame = ctk.CTkFrame(parent)
        activity_frame.grid(row=2, column=0, columnspan=2, padx=10, pady=10, sticky="nsew")
        
        ctk.CTkLabel(
            activity_frame,
            text="📈 Recent Activity",
            font=ctk.CTkFont(size=18, weight="bold")
        ).grid(row=0, column=0, padx=20, pady=15, sticky="w")
        
        # Recent reviews list
        recent_reviews = self.data_manager.get_recent_reviews(limit=5)
        
        for i, review in enumerate(recent_reviews, 1):
            review_text = f"⭐ {review.get('rating', 0)}/5 - {review.get('game_title', 'Unknown Game')}"
            if len(review.get('review_text', '')) > 50:
                review_text += f" - {review.get('review_text', '')[:50]}..."
            
            ctk.CTkLabel(
                activity_frame,
                text=review_text,
                font=ctk.CTkFont(size=12)
            ).grid(row=i, column=0, padx=20, pady=5, sticky="w")
    
    def create_ai_recommendations(self, parent):
        """Create AI recommendations section"""
        rec_frame = ctk.CTkFrame(parent)
        rec_frame.grid(row=2, column=2, padx=10, pady=10, sticky="nsew")
        
        ctk.CTkLabel(
            rec_frame,
            text="🤖 AI Recommendations",
            font=ctk.CTkFont(size=18, weight="bold")
        ).grid(row=0, column=0, padx=20, pady=15, sticky="w")
        
        # Get AI recommendations
        recommendations = self.get_ai_recommendations()
        
        for i, rec in enumerate(recommendations[:5], 1):
            rec_text = f"🎮 {rec.get('title', 'Unknown Game')}"
            
            ctk.CTkLabel(
                rec_frame,
                text=rec_text,
                font=ctk.CTkFont(size=12)
            ).grid(row=i, column=0, padx=20, pady=5, sticky="w")
    
    def show_games(self):
        """Show games view"""
        self.clear_main_content()
        self.update_status("Loading games...")
        
        # Games will be implemented in the next part
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="🎮 Games View - Coming Soon",
            font=ctk.CTkFont(size=24)
        )
        placeholder_label.grid(row=0, column=0, padx=20, pady=20)
        
        self.update_status("Games view loaded")
    
    def show_reviews(self):
        """Show reviews view"""
        self.clear_main_content()
        self.update_status("Loading reviews...")
        
        # Reviews will be implemented in the next part
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="📝 Reviews View - Coming Soon",
            font=ctk.CTkFont(size=24)
        )
        placeholder_label.grid(row=0, column=0, padx=20, pady=20)
        
        self.update_status("Reviews view loaded")
    
    def show_write_review(self):
        """Show write review view"""
        self.clear_main_content()
        self.update_status("Write review form loaded")
        
        # Write review will be implemented in the next part
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="✍️ Write Review - Coming Soon",
            font=ctk.CTkFont(size=24)
        )
        placeholder_label.grid(row=0, column=0, padx=20, pady=20)
    
    def show_ai_insights(self):
        """Show AI insights view"""
        self.clear_main_content()
        self.update_status("Loading AI insights...")
        
        # AI insights will be implemented in the next part
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="🤖 AI Insights - Coming Soon",
            font=ctk.CTkFont(size=24)
        )
        placeholder_label.grid(row=0, column=0, padx=20, pady=20)
    
    def show_analytics(self):
        """Show analytics view"""
        self.clear_main_content()
        self.update_status("Loading analytics...")
        
        # Analytics will be implemented in the next part
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="📊 Analytics - Coming Soon",
            font=ctk.CTkFont(size=24)
        )
        placeholder_label.grid(row=0, column=0, padx=20, pady=20)
    
    def show_settings(self):
        """Show settings view"""
        self.clear_main_content()
        self.update_status("Settings loaded")
        
        # Settings will be implemented in the next part
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="⚙️ Settings - Coming Soon",
            font=ctk.CTkFont(size=24)
        )
        placeholder_label.grid(row=0, column=0, padx=20, pady=20)
    
    def clear_main_content(self):
        """Clear main content area"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()
    
    def toggle_theme(self):
        """Toggle between light and dark themes"""
        if self.theme_switch.get():
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")
    
    def update_status(self, message: str):
        """Update status bar message"""
        self.status_label.configure(text=message)
        self.root.update_idletasks()
    
    def load_initial_data(self):
        """Load initial data in background"""
        def load_data():
            try:
                self.update_status("Loading initial data...")
                
                # Load games and reviews
                self.current_games = self.data_manager.get_all_games()
                self.current_reviews = self.data_manager.get_all_reviews()
                
                self.update_status("Data loaded successfully")
                
            except Exception as e:
                self.update_status(f"Error loading data: {e}")
        
        # Load in background thread
        threading.Thread(target=load_data, daemon=True).start()
    
    def get_ai_recommendations(self) -> List[Dict]:
        """Get AI-powered game recommendations"""
        try:
            # For now, return top-rated games
            # This will be enhanced with actual AI recommendations
            return self.data_manager.get_top_rated_games(limit=5)
        except Exception as e:
            print(f"Error getting AI recommendations: {e}")
            return []
    
    def run(self):
        """Start the application"""
        print("🚀 Starting GameReview Pro...")
        self.root.mainloop()

def main():
    """Main entry point"""
    try:
        app = GameReviewProApp()
        app.run()
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        messagebox.showerror("Error", f"Failed to start application: {e}")

if __name__ == "__main__":
    main()

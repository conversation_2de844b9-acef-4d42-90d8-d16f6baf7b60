@echo off
echo ========================================
echo GameReview Pro - Windows 11 Desktop App
echo Installation Script
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found! Installing dependencies...
echo.

echo Installing CustomTkinter and dependencies...
pip install customtkinter==5.2.2
if errorlevel 1 (
    echo ERROR: Failed to install CustomTkinter
    pause
    exit /b 1
)

echo Installing Pillow for image support...
pip install pillow==10.1.0
if errorlevel 1 (
    echo ERROR: Failed to install Pillow
    pause
    exit /b 1
)

echo Installing additional dependencies...
pip install python-dateutil==2.8.2
if errorlevel 1 (
    echo ERROR: Failed to install python-dateutil
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo You can now run GameReview Pro by:
echo 1. Double-clicking "run.bat"
echo 2. Or running "python main.py" in this folder
echo.
echo Press any key to launch the application...
pause >nul

echo Starting GameReview Pro...
python main.py

pause

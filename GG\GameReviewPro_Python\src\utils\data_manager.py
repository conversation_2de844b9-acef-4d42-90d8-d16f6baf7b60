"""
Data Manager for GameReview Pro Python Application
Handles all data operations and business logic
"""

import json
import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging
from sqlalchemy.orm import sessionmaker
from sqlalchemy import desc, func, and_, or_

from ..database.models import db_manager, Game, Review, User
from ..ai.sentiment_analyzer import sentiment_analyzer
from ..ai.recommendation_engine import recommendation_engine

class DataManager:
    """Centralized data management class"""
    
    def __init__(self):
        self.db = db_manager
        self.session = self.db.get_session()
        print("✅ Data Manager initialized")
    
    def get_all_games(self, limit: int = 100) -> List[Dict]:
        """Get all games with basic info"""
        try:
            games = self.session.query(Game).limit(limit).all()
            return [game.to_dict() for game in games]
        except Exception as e:
            logging.error(f"Error getting games: {e}")
            return []
    
    def get_game_by_id(self, game_id: int) -> Optional[Dict]:
        """Get specific game by ID"""
        try:
            game = self.session.query(Game).filter(Game.id == game_id).first()
            return game.to_dict() if game else None
        except Exception as e:
            logging.error(f"Error getting game {game_id}: {e}")
            return None
    
    def search_games(self, query: str, limit: int = 20) -> List[Dict]:
        """Search games by title, genre, or description"""
        try:
            games = self.session.query(Game).filter(
                or_(
                    Game.title.contains(query),
                    Game.genre.contains(query),
                    Game.description.contains(query),
                    Game.developer.contains(query)
                )
            ).limit(limit).all()
            
            return [game.to_dict() for game in games]
        except Exception as e:
            logging.error(f"Error searching games: {e}")
            return []
    
    def get_games_by_genre(self, genre: str, limit: int = 20) -> List[Dict]:
        """Get games by genre"""
        try:
            games = self.session.query(Game).filter(
                Game.genre.contains(genre)
            ).order_by(desc(Game.average_rating)).limit(limit).all()
            
            return [game.to_dict() for game in games]
        except Exception as e:
            logging.error(f"Error getting games by genre: {e}")
            return []
    
    def get_top_rated_games(self, limit: int = 10) -> List[Dict]:
        """Get top rated games"""
        try:
            games = self.session.query(Game).filter(
                Game.total_reviews > 0
            ).order_by(desc(Game.average_rating), desc(Game.total_reviews)).limit(limit).all()
            
            return [game.to_dict() for game in games]
        except Exception as e:
            logging.error(f"Error getting top rated games: {e}")
            return []
    
    def get_popular_games(self, limit: int = 10) -> List[Dict]:
        """Get most reviewed games"""
        try:
            games = self.session.query(Game).order_by(
                desc(Game.total_reviews), desc(Game.average_rating)
            ).limit(limit).all()
            
            return [game.to_dict() for game in games]
        except Exception as e:
            logging.error(f"Error getting popular games: {e}")
            return []
    
    def get_all_reviews(self, limit: int = 100) -> List[Dict]:
        """Get all reviews"""
        try:
            reviews = self.session.query(Review).order_by(
                desc(Review.created_at)
            ).limit(limit).all()
            
            return [review.to_dict() for review in reviews]
        except Exception as e:
            logging.error(f"Error getting reviews: {e}")
            return []
    
    def get_reviews_for_game(self, game_id: int, limit: int = 50) -> List[Dict]:
        """Get reviews for specific game"""
        try:
            reviews = self.session.query(Review).filter(
                Review.game_id == game_id
            ).order_by(desc(Review.created_at)).limit(limit).all()
            
            return [review.to_dict() for review in reviews]
        except Exception as e:
            logging.error(f"Error getting reviews for game {game_id}: {e}")
            return []
    
    def get_recent_reviews(self, limit: int = 10) -> List[Dict]:
        """Get recent reviews with game info"""
        try:
            reviews = self.session.query(Review).join(Game).order_by(
                desc(Review.created_at)
            ).limit(limit).all()
            
            result = []
            for review in reviews:
                review_dict = review.to_dict()
                review_dict['game_title'] = review.game.title
                review_dict['game_genre'] = review.game.genre
                result.append(review_dict)
            
            return result
        except Exception as e:
            logging.error(f"Error getting recent reviews: {e}")
            return []
    
    def create_review(self, review_data: Dict) -> Optional[int]:
        """Create new review with AI analysis"""
        try:
            # Validate required fields
            required_fields = ['game_id', 'rating']
            for field in required_fields:
                if field not in review_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Check if game exists
            game = self.session.query(Game).filter(Game.id == review_data['game_id']).first()
            if not game:
                raise ValueError("Game not found")
            
            # Perform AI analysis on review text
            review_text = review_data.get('review_text', '')
            ai_analysis = {}
            
            if review_text:
                # Sentiment analysis
                sentiment_result = sentiment_analyzer.analyze_sentiment(review_text)
                ai_analysis.update(sentiment_result)
                
                # Quality analysis
                quality_result = sentiment_analyzer.analyze_review_quality(review_text)
                ai_analysis.update(quality_result)
            
            # Create review object
            review = Review(
                game_id=review_data['game_id'],
                reviewer_name=review_data.get('reviewer_name', 'Anonymous'),
                rating=review_data['rating'],
                review_text=review_text,
                platform=review_data.get('platform', ''),
                hours_played=review_data.get('hours_played', 0.0),
                pros=json.dumps(review_data.get('pros', [])),
                cons=json.dumps(review_data.get('cons', [])),
                
                # AI analysis results
                sentiment_score=ai_analysis.get('sentiment_score', 0.0),
                emotion_scores=json.dumps(ai_analysis.get('emotions', {})),
                readability_score=ai_analysis.get('readability_score', 0.0),
                helpfulness_score=ai_analysis.get('helpfulness_score', 0.0),
                
                image_path=review_data.get('image_path', ''),
                video_path=review_data.get('video_path', '')
            )
            
            # Save review
            self.session.add(review)
            self.session.commit()
            
            # Update game statistics
            self.update_game_statistics(review_data['game_id'])
            
            print(f"✅ Review created with AI analysis: {review.id}")
            return review.id
            
        except Exception as e:
            self.session.rollback()
            logging.error(f"Error creating review: {e}")
            return None
    
    def update_game_statistics(self, game_id: int):
        """Update game's calculated statistics"""
        try:
            # Get all reviews for the game
            reviews = self.session.query(Review).filter(Review.game_id == game_id).all()
            
            if not reviews:
                return
            
            # Calculate statistics
            ratings = [review.rating for review in reviews]
            sentiment_scores = [review.sentiment_score for review in reviews if review.sentiment_score]
            
            average_rating = sum(ratings) / len(ratings)
            total_reviews = len(reviews)
            average_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0.0
            
            # Calculate popularity score (combination of reviews and ratings)
            popularity_score = (average_rating / 5.0) * 0.7 + min(total_reviews / 100.0, 1.0) * 0.3
            
            # Update game
            game = self.session.query(Game).filter(Game.id == game_id).first()
            if game:
                game.average_rating = round(average_rating, 1)
                game.total_reviews = total_reviews
                game.sentiment_score = round(average_sentiment, 2)
                game.popularity_score = round(popularity_score, 2)
                game.updated_at = datetime.utcnow()
                
                self.session.commit()
                print(f"✅ Updated statistics for game {game_id}")
            
        except Exception as e:
            self.session.rollback()
            logging.error(f"Error updating game statistics: {e}")
    
    def get_app_statistics(self) -> Dict:
        """Get overall app statistics"""
        try:
            total_games = self.session.query(Game).count()
            total_reviews = self.session.query(Review).count()
            
            # Average rating across all games
            avg_rating_result = self.session.query(func.avg(Game.average_rating)).filter(
                Game.total_reviews > 0
            ).scalar()
            average_rating = round(avg_rating_result, 1) if avg_rating_result else 0.0
            
            # Recent activity (last 7 days)
            week_ago = datetime.utcnow() - timedelta(days=7)
            recent_reviews = self.session.query(Review).filter(
                Review.created_at >= week_ago
            ).count()
            
            # Most popular genre
            genre_stats = self.session.query(
                Game.genre, func.count(Game.id).label('count')
            ).group_by(Game.genre).order_by(desc('count')).first()
            
            most_popular_genre = genre_stats[0] if genre_stats else 'Unknown'
            
            return {
                'total_games': total_games,
                'total_reviews': total_reviews,
                'average_rating': average_rating,
                'recent_reviews': recent_reviews,
                'most_popular_genre': most_popular_genre
            }
            
        except Exception as e:
            logging.error(f"Error getting app statistics: {e}")
            return {
                'total_games': 0,
                'total_reviews': 0,
                'average_rating': 0.0,
                'recent_reviews': 0,
                'most_popular_genre': 'Unknown'
            }
    
    def get_ai_recommendations(self, user_id: int = None, limit: int = 10) -> List[Dict]:
        """Get AI-powered game recommendations"""
        try:
            # Get user reviews if user_id provided
            user_reviews = []
            if user_id:
                reviews = self.session.query(Review).filter(Review.user_id == user_id).all()
                user_reviews = [review.to_dict() for review in reviews]
            
            # Get all reviews for collaborative filtering
            all_reviews = [review.to_dict() for review in self.session.query(Review).all()]
            
            # Get all games
            games = [game.to_dict() for game in self.session.query(Game).all()]
            
            # Get recommendations from AI engine
            if user_reviews:
                recommendations = recommendation_engine.get_hybrid_recommendations(
                    user_id or 0, user_reviews, all_reviews, games, limit
                )
            else:
                # For new users, recommend top-rated games
                recommendations = self.get_top_rated_games(limit)
                for rec in recommendations:
                    rec['recommendation_score'] = rec.get('average_rating', 0.0) / 5.0
                    rec['recommendation_reason'] = "Highly rated by the community"
            
            return recommendations
            
        except Exception as e:
            logging.error(f"Error getting AI recommendations: {e}")
            return self.get_top_rated_games(limit)
    
    def analyze_review_sentiment_batch(self, review_ids: List[int]):
        """Analyze sentiment for multiple reviews"""
        try:
            reviews = self.session.query(Review).filter(Review.id.in_(review_ids)).all()
            
            for review in reviews:
                if review.review_text and not review.sentiment_score:
                    # Analyze sentiment
                    sentiment_result = sentiment_analyzer.analyze_sentiment(review.review_text)
                    
                    # Update review
                    review.sentiment_score = sentiment_result.get('sentiment_score', 0.0)
                    review.emotion_scores = json.dumps(sentiment_result.get('emotions', {}))
                    
                    # Analyze quality
                    quality_result = sentiment_analyzer.analyze_review_quality(review.review_text)
                    review.readability_score = quality_result.get('readability_score', 0.0)
                    review.helpfulness_score = quality_result.get('helpfulness_score', 0.0)
            
            self.session.commit()
            print(f"✅ Analyzed sentiment for {len(reviews)} reviews")
            
        except Exception as e:
            self.session.rollback()
            logging.error(f"Error in batch sentiment analysis: {e}")
    
    def get_trending_games(self, days: int = 7, limit: int = 10) -> List[Dict]:
        """Get trending games based on recent activity"""
        try:
            # Get games with recent reviews
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            trending = self.session.query(
                Game, func.count(Review.id).label('recent_reviews')
            ).join(Review).filter(
                Review.created_at >= cutoff_date
            ).group_by(Game.id).order_by(
                desc('recent_reviews'), desc(Game.average_rating)
            ).limit(limit).all()
            
            result = []
            for game, recent_count in trending:
                game_dict = game.to_dict()
                game_dict['recent_reviews'] = recent_count
                game_dict['trending_score'] = recent_count * (game.average_rating / 5.0)
                result.append(game_dict)
            
            return result
            
        except Exception as e:
            logging.error(f"Error getting trending games: {e}")
            return []
    
    def close(self):
        """Close database session"""
        if self.session:
            self.session.close()
        if self.db:
            self.db.close()

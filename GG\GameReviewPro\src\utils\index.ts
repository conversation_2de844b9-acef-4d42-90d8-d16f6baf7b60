// Utility functions for GameReview Pro Mobile App

import { Dimensions, Platform } from 'react-native';

// Device utilities
export const getScreenDimensions = () => {
  const { width, height } = Dimensions.get('window');
  return { width, height };
};

export const isIOS = Platform.OS === 'ios';
export const isAndroid = Platform.OS === 'android';

// Date utilities
export const formatDate = (dateString: string, format: 'short' | 'long' | 'relative' = 'relative') => {
  const date = new Date(dateString);
  const now = new Date();
  
  if (format === 'relative') {
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`;
    return `${Math.ceil(diffDays / 365)} years ago`;
  }
  
  if (format === 'short') {
    return date.toLocaleDateString();
  }
  
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Number utilities
export const formatNumber = (num: number): string => {
  if (num < 1000) return num.toString();
  if (num < 1000000) return `${(num / 1000).toFixed(1)}K`;
  return `${(num / 1000000).toFixed(1)}M`;
};

// Rating utilities
export const getRatingColor = (rating: number): string => {
  if (rating >= 4.5) return '#00C851'; // Green
  if (rating >= 3.5) return '#FFD700'; // Gold
  if (rating >= 2.5) return '#FFB300'; // Amber
  return '#FF4444'; // Red
};

export const getRatingText = (rating: number): string => {
  if (rating >= 4.5) return 'Excellent';
  if (rating >= 3.5) return 'Good';
  if (rating >= 2.5) return 'Average';
  if (rating >= 1.5) return 'Poor';
  return 'Terrible';
};

// Platform utilities
export const getPlatformColor = (platform: string): string => {
  const platformColors: { [key: string]: string } = {
    'pc': '#FF6B35',
    'steam': '#1B2838',
    'epic games store': '#313131',
    'playstation 4': '#003087',
    'playstation 5': '#003087',
    'xbox one': '#107C10',
    'xbox series x/s': '#107C10',
    'nintendo switch': '#E60012',
    'ios': '#007AFF',
    'android': '#3DDC84',
    'steam deck': '#1B2838',
  };
  
  return platformColors[platform.toLowerCase()] || '#0066FF';
};

export const getPlatformIcon = (platform: string): string => {
  const platformIcons: { [key: string]: string } = {
    'pc': 'computer',
    'steam': 'computer',
    'epic games store': 'computer',
    'playstation 4': 'sports-esports',
    'playstation 5': 'sports-esports',
    'xbox one': 'videogame-asset',
    'xbox series x/s': 'videogame-asset',
    'nintendo switch': 'games',
    'ios': 'smartphone',
    'android': 'smartphone',
    'steam deck': 'videogame-asset',
  };
  
  return platformIcons[platform.toLowerCase()] || 'videogame-asset';
};

// String utilities
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

export const capitalizeFirst = (text: string): string => {
  return text.charAt(0).toUpperCase() + text.slice(1);
};

export const slugify = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

// Validation utilities
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidRating = (rating: number): boolean => {
  return rating >= 1 && rating <= 5 && Number.isInteger(rating);
};

// Image utilities
export const getImageUrl = (imagePath: string, baseUrl: string = 'http://localhost:5000'): string => {
  if (!imagePath) return '';
  if (imagePath.startsWith('http')) return imagePath;
  return `${baseUrl}${imagePath}`;
};

export const getPlaceholderImage = (width: number = 300, height: number = 400, text: string = 'No Image'): string => {
  return `https://via.placeholder.com/${width}x${height}/1A1A1A/FFFFFF?text=${encodeURIComponent(text)}`;
};

// Array utilities
export const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

export const groupBy = <T>(array: T[], key: keyof T): { [key: string]: T[] } => {
  return array.reduce((groups, item) => {
    const group = String(item[key]);
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {} as { [key: string]: T[] });
};

// Debounce utility
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Storage utilities
export const safeJsonParse = <T>(jsonString: string, fallback: T): T => {
  try {
    return JSON.parse(jsonString);
  } catch {
    return fallback;
  }
};

export const safeJsonStringify = (obj: any): string => {
  try {
    return JSON.stringify(obj);
  } catch {
    return '{}';
  }
};

// Error utilities
export const getErrorMessage = (error: any): string => {
  if (typeof error === 'string') return error;
  if (error?.message) return error.message;
  if (error?.response?.data?.error) return error.response.data.error;
  if (error?.response?.data?.message) return error.response.data.message;
  return 'An unexpected error occurred';
};

// Performance utilities
export const measurePerformance = async <T>(
  name: string,
  fn: () => Promise<T>
): Promise<T> => {
  const start = Date.now();
  try {
    const result = await fn();
    const duration = Date.now() - start;
    console.log(`Performance: ${name} took ${duration}ms`);
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    console.log(`Performance: ${name} failed after ${duration}ms`);
    throw error;
  }
};

export default {
  // Device
  getScreenDimensions,
  isIOS,
  isAndroid,
  
  // Date
  formatDate,
  
  // Number
  formatNumber,
  
  // Rating
  getRatingColor,
  getRatingText,
  
  // Platform
  getPlatformColor,
  getPlatformIcon,
  
  // String
  truncateText,
  capitalizeFirst,
  slugify,
  
  // Validation
  isValidEmail,
  isValidRating,
  
  // Image
  getImageUrl,
  getPlaceholderImage,
  
  // Array
  shuffleArray,
  groupBy,
  
  // Function
  debounce,
  
  // Storage
  safeJsonParse,
  safeJsonStringify,
  
  // Error
  getErrorMessage,
  
  // Performance
  measurePerformance,
};

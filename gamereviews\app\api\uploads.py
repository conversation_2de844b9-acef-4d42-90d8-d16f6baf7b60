"""
File upload API routes for GameReview Application
"""

from flask import request, jsonify
from . import api_bp
from ..utils.file_handler import save_uploaded_file
from ..utils.helpers import create_response

@api_bp.route('/upload/review-image', methods=['POST'])
def upload_review_image():
    """Upload image for review"""
    try:
        # Check if file is in request
        if 'image' not in request.files:
            return jsonify(create_response(False, error='No image file provided')), 400
        
        file = request.files['image']
        
        if file.filename == '':
            return jsonify(create_response(False, error='No file selected')), 400
        
        # Save uploaded file
        image_url, error = save_uploaded_file(file)
        
        if error:
            return jsonify(create_response(False, error=error)), 400
        
        return jsonify(create_response(
            True,
            data={
                'image_url': image_url,
                'filename': file.filename
            },
            message='Image uploaded successfully'
        ))
        
    except Exception as e:
        return jsonify(create_response(False, error=str(e))), 500

@api_bp.route('/upload/game-cover', methods=['POST'])
def upload_game_cover():
    """Upload cover image for game (admin functionality)"""
    try:
        # Check if file is in request
        if 'image' not in request.files:
            return jsonify(create_response(False, error='No image file provided')), 400
        
        file = request.files['image']
        
        if file.filename == '':
            return jsonify(create_response(False, error='No file selected')), 400
        
        # Save uploaded file
        image_url, error = save_uploaded_file(file)
        
        if error:
            return jsonify(create_response(False, error=error)), 400
        
        return jsonify(create_response(
            True,
            data={
                'image_url': image_url,
                'filename': file.filename
            },
            message='Game cover uploaded successfully'
        ))
        
    except Exception as e:
        return jsonify(create_response(False, error=str(e))), 500

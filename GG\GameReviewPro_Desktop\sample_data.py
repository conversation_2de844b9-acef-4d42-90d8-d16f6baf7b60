"""
Sample data loader for GameReview Pro Desktop Application
Populates the database with realistic game and review data
"""

from database import GameReviewDatabase
import random
from datetime import datetime, timedelta

def load_sample_data(db: GameReviewDatabase):
    """Load comprehensive sample data into the database"""
    
    # Check if data already exists
    stats = db.get_statistics()
    if stats['total_games'] > 0:
        print("✅ Sample data already exists, skipping...")
        return
    
    print("📊 Loading sample data...")
    
    # Sample games data
    games_data = [
        {
            'title': 'God of War',
            'description': '<PERSON> and his son <PERSON><PERSON> on their journey through Norse mythology in this epic action-adventure.',
            'genre': 'Action/Adventure',
            'platforms': ['PC', 'PlayStation 4', 'PlayStation 5'],
            'developer': 'Santa Monica Studio',
            'publisher': 'Sony Interactive Entertainment',
            'release_date': '2018-04-20',
            'price': 39.99,
            'cover_image': 'god_of_war.jpg'
        },
        {
            'title': 'The Witcher 3: Wild Hunt',
            'description': 'Hunt monsters and explore a vast open world as <PERSON><PERSON><PERSON> of <PERSON><PERSON><PERSON> in this critically acclaimed RPG.',
            'genre': 'RPG',
            'platforms': ['PC', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S', 'Nintendo Switch'],
            'developer': 'CD Projekt Red',
            'publisher': 'CD Projekt',
            'release_date': '2015-05-19',
            'price': 39.99,
            'cover_image': 'witcher3.jpg'
        },
        {
            'title': 'Elden Ring',
            'description': 'A fantasy action-RPG adventure set in the Lands Between, created by FromSoftware and George R.R. Martin.',
            'genre': 'RPG',
            'platforms': ['PC', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S'],
            'developer': 'FromSoftware',
            'publisher': 'Bandai Namco Entertainment',
            'release_date': '2022-02-25',
            'price': 59.99,
            'cover_image': 'elden_ring.jpg'
        },
        {
            'title': 'Cyberpunk 2077',
            'description': 'An open-world, action-adventure story set in Night City, a megalopolis obsessed with power, glamour and body modification.',
            'genre': 'RPG',
            'platforms': ['PC', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S'],
            'developer': 'CD Projekt Red',
            'publisher': 'CD Projekt',
            'release_date': '2020-12-10',
            'price': 29.99,
            'cover_image': 'cyberpunk2077.jpg'
        },
        {
            'title': 'Hades',
            'description': 'Defy the god of the dead in this rogue-like dungeon crawler from the creators of Bastion and Transistor.',
            'genre': 'Indie',
            'platforms': ['PC', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S', 'Nintendo Switch'],
            'developer': 'Supergiant Games',
            'publisher': 'Supergiant Games',
            'release_date': '2020-09-17',
            'price': 24.99,
            'cover_image': 'hades.jpg'
        },
        {
            'title': 'Red Dead Redemption 2',
            'description': 'An epic tale of life in America\'s unforgiving heartland, featuring Arthur Morgan and the Van der Linde gang.',
            'genre': 'Action/Adventure',
            'platforms': ['PC', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S'],
            'developer': 'Rockstar Games',
            'publisher': 'Rockstar Games',
            'release_date': '2018-10-26',
            'price': 59.99,
            'cover_image': 'rdr2.jpg'
        },
        {
            'title': 'Stardew Valley',
            'description': 'Build the farm of your dreams in this charming countryside RPG with endless possibilities.',
            'genre': 'Simulation',
            'platforms': ['PC', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S', 'Nintendo Switch', 'iOS', 'Android'],
            'developer': 'ConcernedApe',
            'publisher': 'ConcernedApe',
            'release_date': '2016-02-26',
            'price': 14.99,
            'cover_image': 'stardew_valley.jpg'
        },
        {
            'title': 'Valorant',
            'description': 'A 5v5 character-based tactical shooter where precise gunplay meets unique agent abilities.',
            'genre': 'Shooter',
            'platforms': ['PC'],
            'developer': 'Riot Games',
            'publisher': 'Riot Games',
            'release_date': '2020-06-02',
            'price': 0.00,
            'cover_image': 'valorant.jpg'
        },
        {
            'title': 'Minecraft',
            'description': 'Build, explore, and survive in infinite worlds made of blocks in this creative sandbox game.',
            'genre': 'Sandbox',
            'platforms': ['PC', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S', 'Nintendo Switch', 'iOS', 'Android'],
            'developer': 'Mojang Studios',
            'publisher': 'Microsoft',
            'release_date': '2011-11-18',
            'price': 26.95,
            'cover_image': 'minecraft.jpg'
        },
        {
            'title': 'Among Us',
            'description': 'Play online or over local WiFi with 4-15 players as you attempt to prep your spaceship for departure.',
            'genre': 'Party',
            'platforms': ['PC', 'iOS', 'Android', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S', 'Nintendo Switch'],
            'developer': 'InnerSloth',
            'publisher': 'InnerSloth',
            'release_date': '2018-06-15',
            'price': 4.99,
            'cover_image': 'among_us.jpg'
        },
        {
            'title': 'Call of Duty: Modern Warfare II',
            'description': 'The ultimate weapon is team in this multiplayer combat experience featuring Task Force 141.',
            'genre': 'Shooter',
            'platforms': ['PC', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S'],
            'developer': 'Infinity Ward',
            'publisher': 'Activision',
            'release_date': '2022-10-28',
            'price': 69.99,
            'cover_image': 'cod_mw2.jpg'
        },
        {
            'title': 'Hollow Knight',
            'description': 'Forge your own path in Hollow Knight! An epic action adventure through a vast ruined kingdom of insects and heroes.',
            'genre': 'Metroidvania',
            'platforms': ['PC', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S', 'Nintendo Switch'],
            'developer': 'Team Cherry',
            'publisher': 'Team Cherry',
            'release_date': '2017-02-24',
            'price': 14.99,
            'cover_image': 'hollow_knight.jpg'
        },
        {
            'title': 'FIFA 24',
            'description': 'The world\'s game with HyperMotionV technology and the most authentic football experience.',
            'genre': 'Sports',
            'platforms': ['PC', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S', 'Nintendo Switch'],
            'developer': 'EA Sports',
            'publisher': 'Electronic Arts',
            'release_date': '2023-09-29',
            'price': 69.99,
            'cover_image': 'fifa24.jpg'
        },
        {
            'title': 'Baldur\'s Gate 3',
            'description': 'A next-generation RPG set in the world of Dungeons & Dragons with unprecedented freedom and choice.',
            'genre': 'RPG',
            'platforms': ['PC', 'PlayStation 5', 'Xbox Series X/S'],
            'developer': 'Larian Studios',
            'publisher': 'Larian Studios',
            'release_date': '2023-08-03',
            'price': 59.99,
            'cover_image': 'baldurs_gate3.jpg'
        },
        {
            'title': 'The Legend of Zelda: Breath of the Wild',
            'description': 'Enter a world of discovery, exploration, and adventure in this open-world Zelda adventure.',
            'genre': 'Adventure',
            'platforms': ['Nintendo Switch'],
            'developer': 'Nintendo',
            'publisher': 'Nintendo',
            'release_date': '2017-03-03',
            'price': 59.99,
            'cover_image': 'zelda_botw.jpg'
        }
    ]
    
    # Add games to database
    game_ids = []
    for game_data in games_data:
        game_id = db.add_game(**game_data)
        game_ids.append(game_id)
    
    print(f"✅ Added {len(games_data)} games")
    
    # Sample reviews data
    reviewer_names = [
        'GameMaster2023', 'ProGamer', 'CasualPlayer', 'HardcoreGamer', 'IndieExplorer',
        'RPGLover', 'ActionFan', 'StrategyExpert', 'HorrorEnthusiast', 'SportsGamer',
        'RetroGamer', 'NextGenPlayer', 'GameCritic', 'PlayerOne', 'GamerGirl',
        'ConsoleWarrior', 'PCMasterRace', 'MobileGamer', 'StreamerPro', 'EsportsPlayer'
    ]
    
    platforms = ['PC', 'PlayStation 5', 'PlayStation 4', 'Xbox Series X/S', 'Xbox One', 'Nintendo Switch']
    
    # Review templates with different sentiments
    review_templates = [
        {
            'rating': 5,
            'text': 'Absolutely incredible game! The story is captivating and the gameplay is smooth and engaging. Every aspect is polished to perfection.',
            'pros': ['Amazing story', 'Great gameplay', 'Beautiful graphics', 'Excellent sound design'],
            'cons': []
        },
        {
            'rating': 5,
            'text': 'This game exceeded all my expectations. The world-building is phenomenal and the characters are unforgettable.',
            'pros': ['Immersive world', 'Memorable characters', 'Outstanding visuals', 'Perfect pacing'],
            'cons': ['Minor loading times']
        },
        {
            'rating': 4,
            'text': 'Really solid game overall. Great mechanics and interesting world, though it has a few minor issues that prevent it from being perfect.',
            'pros': ['Good gameplay', 'Interesting world', 'Nice graphics', 'Solid mechanics'],
            'cons': ['Some minor bugs', 'Could use more content']
        },
        {
            'rating': 4,
            'text': 'Enjoyable experience with great attention to detail. The gameplay loop is addictive and the presentation is top-notch.',
            'pros': ['Addictive gameplay', 'Great presentation', 'Good value', 'Responsive controls'],
            'cons': ['Repetitive at times', 'Some difficulty spikes']
        },
        {
            'rating': 3,
            'text': 'Decent game but nothing special. It\'s enjoyable enough but doesn\'t really stand out from other games in the genre.',
            'pros': ['Okay gameplay', 'Decent graphics', 'Fair price'],
            'cons': ['Generic story', 'Repetitive gameplay', 'Not very innovative']
        },
        {
            'rating': 3,
            'text': 'Mixed feelings about this one. Has some great moments but also some frustrating design choices.',
            'pros': ['Some great moments', 'Good concept', 'Nice art style'],
            'cons': ['Inconsistent quality', 'Frustrating mechanics', 'Pacing issues']
        },
        {
            'rating': 2,
            'text': 'Disappointing experience. The game has potential but is held back by numerous issues and poor execution.',
            'pros': ['Good concept', 'Nice visuals'],
            'cons': ['Poor execution', 'Many bugs', 'Boring gameplay', 'Weak story']
        }
    ]
    
    # Generate reviews for each game
    total_reviews = 0
    for game_id in game_ids:
        num_reviews = random.randint(2, 8)  # 2-8 reviews per game
        
        for _ in range(num_reviews):
            template = random.choice(review_templates)
            reviewer = random.choice(reviewer_names)
            platform = random.choice(platforms)
            hours_played = round(random.uniform(5.0, 200.0), 1)
            
            # Add some variation to the rating
            rating = template['rating']
            if random.random() < 0.2:  # 20% chance to vary rating by 1
                rating = max(1, min(5, rating + random.choice([-1, 1])))
            
            db.add_review(
                game_id=game_id,
                reviewer_name=reviewer,
                rating=rating,
                review_text=template['text'],
                platform=platform,
                hours_played=hours_played,
                pros=template['pros'],
                cons=template['cons']
            )
            total_reviews += 1
    
    print(f"✅ Added {total_reviews} reviews")
    print("✅ Sample data loading completed!")

if __name__ == "__main__":
    # Test the sample data loader
    db = GameReviewDatabase("test_gamereviews.db")
    load_sample_data(db)
    
    # Print statistics
    stats = db.get_statistics()
    print(f"\n📊 Database Statistics:")
    print(f"   Games: {stats['total_games']}")
    print(f"   Reviews: {stats['total_reviews']}")
    print(f"   Average Rating: {stats['average_rating']}")
    print(f"   Popular Genre: {stats['popular_genre']}")
    
    db.close()

"""
Reviews API routes for GameReview Application
"""

from flask import request, jsonify
from . import api_bp
from ..models import Game, Review
from ..utils.validators import validate_review_data, validate_pagination_params
from ..utils.helpers import create_response, extract_pros_cons

@api_bp.route('/reviews', methods=['GET'])
def get_reviews():
    """Get latest reviews with optional filtering"""
    try:
        # Get query parameters
        page = request.args.get('page', 1)
        limit = request.args.get('limit', 20)
        game_id = request.args.get('game_id')
        
        # Validate pagination parameters
        page, limit, validation_errors = validate_pagination_params(page, limit)
        
        # Convert game_id to int if provided
        if game_id:
            try:
                game_id = int(game_id)
            except (ValueError, TypeError):
                return jsonify(create_response(False, error='Invalid game_id parameter')), 400
        
        # Get reviews from database
        reviews = Review.get_all(page=page, limit=limit, game_id=game_id)
        
        response = {
            'success': True,
            'reviews': reviews
        }
        
        # Add validation warnings if any
        if validation_errors:
            response['warnings'] = validation_errors
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify(create_response(False, error=str(e))), 500

@api_bp.route('/reviews/<int:review_id>', methods=['GET'])
def get_review(review_id):
    """Get specific review by ID and increment view count"""
    try:
        review = Review.get_by_id(review_id)
        
        if not review:
            return jsonify(create_response(False, error='Review not found')), 404
        
        return jsonify(create_response(True, data=review))
        
    except Exception as e:
        return jsonify(create_response(False, error=str(e))), 500

@api_bp.route('/reviews', methods=['POST'])
def create_review():
    """Create new review"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify(create_response(False, error='No data provided')), 400
        
        # Validate review data
        is_valid, errors = validate_review_data(data)
        if not is_valid:
            return jsonify(create_response(False, error='; '.join(errors))), 400
        
        # Check if game exists
        game = Game.get_by_id(data['game_id'])
        if not game:
            return jsonify(create_response(False, error='Game not found')), 404
        
        # Extract and format pros/cons
        pros_str, cons_str = extract_pros_cons(
            data.get('pros', []), 
            data.get('cons', [])
        )
        
        # Create review object
        review = Review(
            game_id=data['game_id'],
            reviewer_name=data.get('reviewer_name', 'Anonymous').strip() or 'Anonymous',
            rating=int(data['rating']),
            review_text=data.get('review_text', '').strip(),
            platform=data.get('platform', '').strip(),
            pros=pros_str,
            cons=cons_str,
            image_path=data.get('image_path')
        )
        
        # Save review
        review_id = review.save()
        
        return jsonify(create_response(
            True, 
            data={'review_id': review_id},
            message='Review created successfully'
        )), 201
        
    except Exception as e:
        return jsonify(create_response(False, error=str(e))), 500

@api_bp.route('/reviews/<int:review_id>/like', methods=['PUT'])
def like_review(review_id):
    """Like a review (increment like count)"""
    try:
        # Create review object to use like method
        review = Review(id=review_id)
        new_likes_count = review.like()
        
        if new_likes_count == 0:
            return jsonify(create_response(False, error='Review not found')), 404
        
        return jsonify(create_response(
            True,
            data={'likes_count': new_likes_count},
            message='Review liked successfully'
        ))
        
    except Exception as e:
        return jsonify(create_response(False, error=str(e))), 500

@api_bp.route('/reviews/game/<int:game_id>', methods=['GET'])
def get_reviews_for_game(game_id):
    """Get all reviews for a specific game"""
    try:
        # Check if game exists
        game = Game.get_by_id(game_id)
        if not game:
            return jsonify(create_response(False, error='Game not found')), 404
        
        # Get query parameters
        page = request.args.get('page', 1)
        limit = request.args.get('limit', 20)
        sort_by = request.args.get('sort', 'newest')
        
        # Validate pagination parameters
        page, limit, validation_errors = validate_pagination_params(page, limit)
        
        # Validate sort parameter
        valid_sorts = ['newest', 'oldest', 'rating_high', 'rating_low', 'likes']
        if sort_by not in valid_sorts:
            sort_by = 'newest'
        
        # Get reviews for game
        reviews = Review.get_for_game(
            game_id=game_id,
            page=page,
            limit=limit,
            sort_by=sort_by
        )
        
        response = {
            'success': True,
            'reviews': reviews,
            'game_id': game_id,
            'sort_by': sort_by
        }
        
        # Add validation warnings if any
        if validation_errors:
            response['warnings'] = validation_errors
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify(create_response(False, error=str(e))), 500

@api_bp.route('/reviews/<int:review_id>/image', methods=['PUT'])
def add_image_to_review(review_id):
    """Add image to existing review"""
    try:
        data = request.get_json()
        
        if not data or not data.get('image_path'):
            return jsonify(create_response(False, error='image_path is required')), 400
        
        # Check if review exists and update image
        from ..database import execute_query, execute_update
        
        # Check if review exists
        rows = execute_query("SELECT id FROM reviews WHERE id = ?", (review_id,))
        if not rows:
            return jsonify(create_response(False, error='Review not found')), 404
        
        # Update review with image
        execute_update(
            "UPDATE reviews SET image_path = ? WHERE id = ?",
            (data['image_path'], review_id)
        )
        
        return jsonify(create_response(
            True,
            message='Image added to review successfully'
        ))
        
    except Exception as e:
        return jsonify(create_response(False, error=str(e))), 500

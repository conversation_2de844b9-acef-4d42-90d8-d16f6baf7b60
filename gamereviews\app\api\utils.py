"""
Utility API routes for GameReview Application
"""

from flask import jsonify
from . import api_bp
from ..models import GAMING_PLATFORMS, GAME_GENRES
from ..database import execute_query
from ..utils.helpers import create_response

@api_bp.route('/platforms', methods=['GET'])
def get_platforms():
    """Get list of gaming platforms"""
    return jsonify(create_response(
        True,
        data=GAMING_PLATFORMS,
        message=f"Available gaming platforms ({len(GAMING_PLATFORMS)} total)"
    ))

@api_bp.route('/genres', methods=['GET'])
def get_genres():
    """Get list of game genres"""
    return jsonify(create_response(
        True,
        data=GAME_GENRES,
        message=f"Available game genres ({len(GAME_GENRES)} total)"
    ))

@api_bp.route('/stats', methods=['GET'])
def get_stats():
    """Get app statistics"""
    try:
        # Get total games
        games_result = execute_query("SELECT COUNT(*) as total FROM games")
        total_games = games_result[0]['total'] if games_result else 0
        
        # Get total reviews
        reviews_result = execute_query("SELECT COUNT(*) as total FROM reviews")
        total_reviews = reviews_result[0]['total'] if reviews_result else 0
        
        # Get average rating across all games
        avg_result = execute_query(
            "SELECT AVG(average_rating) as avg FROM games WHERE total_reviews > 0"
        )
        overall_avg_rating = round(avg_result[0]['avg'], 1) if avg_result and avg_result[0]['avg'] else 0.0
        
        # Get most popular genre
        genre_result = execute_query(
            """SELECT genre, COUNT(*) as count 
               FROM games 
               WHERE genre IS NOT NULL 
               GROUP BY genre 
               ORDER BY count DESC 
               LIMIT 1"""
        )
        most_popular_genre = genre_result[0]['genre'] if genre_result else 'Unknown'
        
        # Get most active platform
        platform_result = execute_query(
            """SELECT platform, COUNT(*) as count 
               FROM reviews 
               WHERE platform IS NOT NULL AND platform != ''
               GROUP BY platform 
               ORDER BY count DESC 
               LIMIT 1"""
        )
        most_active_platform = platform_result[0]['platform'] if platform_result else 'Unknown'
        
        stats = {
            'total_games': total_games,
            'total_reviews': total_reviews,
            'overall_average_rating': overall_avg_rating,
            'most_popular_genre': most_popular_genre,
            'most_active_platform': most_active_platform
        }
        
        return jsonify(create_response(
            True,
            data=stats,
            message='App statistics retrieved successfully'
        ))
        
    except Exception as e:
        return jsonify(create_response(False, error=str(e))), 500

@api_bp.route('/search/suggestions', methods=['GET'])
def get_search_suggestions():
    """Get search suggestions based on popular games"""
    try:
        # Get top games by review count for suggestions
        suggestions_result = execute_query(
            """SELECT title 
               FROM games 
               WHERE total_reviews > 0 
               ORDER BY total_reviews DESC, average_rating DESC 
               LIMIT 10"""
        )
        
        suggestions = [row['title'] for row in suggestions_result]
        
        return jsonify(create_response(
            True,
            data=suggestions,
            message=f"Top {len(suggestions)} search suggestions"
        ))
        
    except Exception as e:
        return jsonify(create_response(False, error=str(e))), 500

@api_bp.route('/trending', methods=['GET'])
def get_trending():
    """Get trending games and reviews"""
    try:
        # Get recently reviewed games (trending)
        trending_games = execute_query(
            """SELECT g.*, COUNT(r.id) as recent_reviews
               FROM games g
               JOIN reviews r ON g.id = r.game_id
               WHERE r.created_at >= datetime('now', '-7 days')
               GROUP BY g.id
               ORDER BY recent_reviews DESC, g.average_rating DESC
               LIMIT 5"""
        )
        
        # Get most liked reviews from last week
        trending_reviews = execute_query(
            """SELECT r.*, g.title as game_title, g.cover_image as game_cover
               FROM reviews r
               JOIN games g ON r.game_id = g.id
               WHERE r.created_at >= datetime('now', '-7 days')
               ORDER BY r.likes_count DESC, r.views_count DESC
               LIMIT 5"""
        )
        
        # Format the data
        games_data = []
        for row in trending_games:
            game_dict = {
                'id': row['id'],
                'title': row['title'],
                'description': row['description'],
                'genre': row['genre'],
                'platforms': row['platforms'].split(',') if row['platforms'] else [],
                'cover_image': row['cover_image'],
                'average_rating': row['average_rating'],
                'total_reviews': row['total_reviews'],
                'recent_reviews': row['recent_reviews']
            }
            games_data.append(game_dict)
        
        reviews_data = []
        for row in trending_reviews:
            review_dict = {
                'id': row['id'],
                'game_id': row['game_id'],
                'game_title': row['game_title'],
                'game_cover': row['game_cover'],
                'reviewer_name': row['reviewer_name'],
                'rating': row['rating'],
                'review_text': row['review_text'],
                'platform': row['platform'],
                'likes_count': row['likes_count'],
                'views_count': row['views_count'],
                'created_at': row['created_at']
            }
            reviews_data.append(review_dict)
        
        trending_data = {
            'trending_games': games_data,
            'trending_reviews': reviews_data
        }
        
        return jsonify(create_response(
            True,
            data=trending_data,
            message='Trending content retrieved successfully'
        ))
        
    except Exception as e:
        return jsonify(create_response(False, error=str(e))), 500

// Professional Theme Configuration
import { Theme } from '../types';

export const theme: Theme = {
  colors: {
    // Professional Dark Theme with Blue Accents
    primary: '#0066FF',        // Professional Blue
    secondary: '#4A90E2',      // Lighter Blue
    background: '#0A0A0A',     // Deep Black
    surface: '#1A1A1A',       // Dark Gray Cards
    text: '#FFFFFF',          // Pure White
    textSecondary: '#B3B3B3',  // Light Gray
    accent: '#00D4FF',        // <PERSON>an Accent
    success: '#00C851',       // Green
    warning: '#FFB300',       // Amber
    error: '#FF4444',         // Red
    border: '#333333',        // Border Gray
    shadow: '#000000',        // Shadow Black
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  typography: {
    h1: 28,
    h2: 24,
    h3: 20,
    body: 16,
    caption: 14,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
  },
};

// Professional Color Palette
export const colors = {
  // Primary Colors
  primaryBlue: '#0066FF',
  primaryBlueDark: '#0052CC',
  primaryBlueLight: '#3385FF',
  
  // Neutral Colors
  black: '#0A0A0A',
  darkGray: '#1A1A1A',
  mediumGray: '#333333',
  lightGray: '#B3B3B3',
  white: '#FFFFFF',
  
  // Accent Colors
  cyan: '#00D4FF',
  gold: '#FFD700',
  green: '#00C851',
  amber: '#FFB300',
  red: '#FF4444',
  
  // Gradient Colors
  gradientStart: '#0066FF',
  gradientEnd: '#00D4FF',
  
  // Platform Colors
  pc: '#FF6B35',
  playstation: '#003087',
  xbox: '#107C10',
  nintendo: '#E60012',
  mobile: '#34C759',
  steam: '#1B2838',
};

// Professional Shadows
export const shadows = {
  small: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  medium: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
  large: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
    elevation: 16,
  },
};

// Typography Styles
export const typography = {
  h1: {
    fontSize: 28,
    fontWeight: '700' as const,
    color: colors.white,
    letterSpacing: -0.5,
  },
  h2: {
    fontSize: 24,
    fontWeight: '600' as const,
    color: colors.white,
    letterSpacing: -0.3,
  },
  h3: {
    fontSize: 20,
    fontWeight: '600' as const,
    color: colors.white,
    letterSpacing: -0.2,
  },
  body: {
    fontSize: 16,
    fontWeight: '400' as const,
    color: colors.white,
    lineHeight: 24,
  },
  bodySecondary: {
    fontSize: 16,
    fontWeight: '400' as const,
    color: colors.lightGray,
    lineHeight: 24,
  },
  caption: {
    fontSize: 14,
    fontWeight: '400' as const,
    color: colors.lightGray,
    lineHeight: 20,
  },
  button: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: colors.white,
  },
};

// Layout Constants
export const layout = {
  screenPadding: 16,
  cardPadding: 16,
  borderRadius: 12,
  headerHeight: 60,
  tabBarHeight: 80,
  buttonHeight: 48,
  inputHeight: 48,
};

// Animation Durations
export const animations = {
  fast: 200,
  normal: 300,
  slow: 500,
};

export default theme;

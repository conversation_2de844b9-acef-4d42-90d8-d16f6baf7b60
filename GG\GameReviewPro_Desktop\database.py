"""
Database module for GameReview Pro Desktop Application
Local SQLite database with comprehensive game and review management
"""

import sqlite3
import json
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple

class GameReviewDatabase:
    """Local SQLite database manager for GameReview Pro"""
    
    def __init__(self, db_path: str = "gamereviews.db"):
        """Initialize database connection and create tables if needed"""
        self.db_path = db_path
        self.init_database()
        print(f"✅ Database initialized: {db_path}")
    
    def init_database(self):
        """Create database tables if they don't exist"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create games table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS games (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL UNIQUE,
                    description TEXT,
                    genre TEXT,
                    platforms TEXT,  -- JSO<PERSON> array as string
                    developer TEXT,
                    publisher TEXT,
                    release_date TEXT,
                    price REAL DEFAULT 0.0,
                    cover_image TEXT,
                    average_rating REAL DEFAULT 0.0,
                    total_reviews INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create reviews table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reviews (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    game_id INTEGER NOT NULL,
                    reviewer_name TEXT DEFAULT 'Anonymous',
                    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
                    review_text TEXT,
                    platform TEXT,
                    hours_played REAL DEFAULT 0.0,
                    pros TEXT,  -- JSON array as string
                    cons TEXT,  -- JSON array as string
                    helpful_votes INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (game_id) REFERENCES games (id) ON DELETE CASCADE
                )
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_games_genre ON games(genre)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_games_rating ON games(average_rating)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_reviews_game_id ON reviews(game_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_reviews_rating ON reviews(rating)')
            
            conn.commit()
    
    def add_game(self, title: str, description: str = "", genre: str = "", 
                 platforms: List[str] = None, developer: str = "", 
                 publisher: str = "", release_date: str = "", 
                 price: float = 0.0, cover_image: str = "") -> int:
        """Add a new game to the database"""
        platforms_json = json.dumps(platforms or [])
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO games (title, description, genre, platforms, developer, 
                                 publisher, release_date, price, cover_image)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (title, description, genre, platforms_json, developer, 
                  publisher, release_date, price, cover_image))
            
            game_id = cursor.lastrowid
            conn.commit()
            return game_id
    
    def add_review(self, game_id: int, reviewer_name: str, rating: int, 
                   review_text: str = "", platform: str = "", 
                   hours_played: float = 0.0, pros: List[str] = None, 
                   cons: List[str] = None) -> int:
        """Add a new review to the database"""
        pros_json = json.dumps(pros or [])
        cons_json = json.dumps(cons or [])
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO reviews (game_id, reviewer_name, rating, review_text, 
                                   platform, hours_played, pros, cons)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (game_id, reviewer_name, rating, review_text, platform, 
                  hours_played, pros_json, cons_json))
            
            review_id = cursor.lastrowid
            
            # Update game statistics
            self.update_game_stats(game_id)
            
            conn.commit()
            return review_id
    
    def update_game_stats(self, game_id: int):
        """Update game's average rating and review count"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Calculate new statistics
            cursor.execute('''
                SELECT AVG(rating), COUNT(*) 
                FROM reviews 
                WHERE game_id = ?
            ''', (game_id,))
            
            result = cursor.fetchone()
            avg_rating = round(result[0], 1) if result[0] else 0.0
            total_reviews = result[1]
            
            # Update game record
            cursor.execute('''
                UPDATE games 
                SET average_rating = ?, total_reviews = ?
                WHERE id = ?
            ''', (avg_rating, total_reviews, game_id))
            
            conn.commit()
    
    def get_all_games(self, limit: int = 100) -> List[Dict]:
        """Get all games with their statistics"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, title, description, genre, platforms, developer, 
                       publisher, release_date, price, cover_image, 
                       average_rating, total_reviews, created_at
                FROM games 
                ORDER BY average_rating DESC, total_reviews DESC
                LIMIT ?
            ''', (limit,))
            
            games = []
            for row in cursor.fetchall():
                game = {
                    'id': row[0],
                    'title': row[1],
                    'description': row[2],
                    'genre': row[3],
                    'platforms': json.loads(row[4]) if row[4] else [],
                    'developer': row[5],
                    'publisher': row[6],
                    'release_date': row[7],
                    'price': row[8],
                    'cover_image': row[9],
                    'average_rating': row[10],
                    'total_reviews': row[11],
                    'created_at': row[12]
                }
                games.append(game)
            
            return games
    
    def search_games(self, query: str) -> List[Dict]:
        """Search games by title, genre, or developer"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            search_pattern = f"%{query}%"
            cursor.execute('''
                SELECT id, title, description, genre, platforms, developer, 
                       publisher, release_date, price, cover_image, 
                       average_rating, total_reviews, created_at
                FROM games 
                WHERE title LIKE ? OR genre LIKE ? OR developer LIKE ?
                ORDER BY average_rating DESC
            ''', (search_pattern, search_pattern, search_pattern))
            
            games = []
            for row in cursor.fetchall():
                game = {
                    'id': row[0],
                    'title': row[1],
                    'description': row[2],
                    'genre': row[3],
                    'platforms': json.loads(row[4]) if row[4] else [],
                    'developer': row[5],
                    'publisher': row[6],
                    'release_date': row[7],
                    'price': row[8],
                    'cover_image': row[9],
                    'average_rating': row[10],
                    'total_reviews': row[11],
                    'created_at': row[12]
                }
                games.append(game)
            
            return games
    
    def get_games_by_genre(self, genre: str) -> List[Dict]:
        """Get games filtered by genre"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, title, description, genre, platforms, developer, 
                       publisher, release_date, price, cover_image, 
                       average_rating, total_reviews, created_at
                FROM games 
                WHERE genre = ?
                ORDER BY average_rating DESC
            ''', (genre,))
            
            games = []
            for row in cursor.fetchall():
                game = {
                    'id': row[0],
                    'title': row[1],
                    'description': row[2],
                    'genre': row[3],
                    'platforms': json.loads(row[4]) if row[4] else [],
                    'developer': row[5],
                    'publisher': row[6],
                    'release_date': row[7],
                    'price': row[8],
                    'cover_image': row[9],
                    'average_rating': row[10],
                    'total_reviews': row[11],
                    'created_at': row[12]
                }
                games.append(game)
            
            return games
    
    def get_game_by_id(self, game_id: int) -> Optional[Dict]:
        """Get a specific game by ID"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, title, description, genre, platforms, developer, 
                       publisher, release_date, price, cover_image, 
                       average_rating, total_reviews, created_at
                FROM games 
                WHERE id = ?
            ''', (game_id,))
            
            row = cursor.fetchone()
            if row:
                return {
                    'id': row[0],
                    'title': row[1],
                    'description': row[2],
                    'genre': row[3],
                    'platforms': json.loads(row[4]) if row[4] else [],
                    'developer': row[5],
                    'publisher': row[6],
                    'release_date': row[7],
                    'price': row[8],
                    'cover_image': row[9],
                    'average_rating': row[10],
                    'total_reviews': row[11],
                    'created_at': row[12]
                }
            return None
    
    def get_reviews_for_game(self, game_id: int) -> List[Dict]:
        """Get all reviews for a specific game"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT r.id, r.game_id, r.reviewer_name, r.rating, r.review_text,
                       r.platform, r.hours_played, r.pros, r.cons, r.helpful_votes,
                       r.created_at, g.title as game_title
                FROM reviews r
                JOIN games g ON r.game_id = g.id
                WHERE r.game_id = ?
                ORDER BY r.created_at DESC
            ''', (game_id,))
            
            reviews = []
            for row in cursor.fetchall():
                review = {
                    'id': row[0],
                    'game_id': row[1],
                    'reviewer_name': row[2],
                    'rating': row[3],
                    'review_text': row[4],
                    'platform': row[5],
                    'hours_played': row[6],
                    'pros': json.loads(row[7]) if row[7] else [],
                    'cons': json.loads(row[8]) if row[8] else [],
                    'helpful_votes': row[9],
                    'created_at': row[10],
                    'game_title': row[11]
                }
                reviews.append(review)
            
            return reviews
    
    def get_recent_reviews(self, limit: int = 10) -> List[Dict]:
        """Get recent reviews across all games"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT r.id, r.game_id, r.reviewer_name, r.rating, r.review_text,
                       r.platform, r.hours_played, r.pros, r.cons, r.helpful_votes,
                       r.created_at, g.title as game_title
                FROM reviews r
                JOIN games g ON r.game_id = g.id
                ORDER BY r.created_at DESC
                LIMIT ?
            ''', (limit,))
            
            reviews = []
            for row in cursor.fetchall():
                review = {
                    'id': row[0],
                    'game_id': row[1],
                    'reviewer_name': row[2],
                    'rating': row[3],
                    'review_text': row[4],
                    'platform': row[5],
                    'hours_played': row[6],
                    'pros': json.loads(row[7]) if row[7] else [],
                    'cons': json.loads(row[8]) if row[8] else [],
                    'helpful_votes': row[9],
                    'created_at': row[10],
                    'game_title': row[11]
                }
                reviews.append(review)
            
            return reviews
    
    def get_statistics(self) -> Dict:
        """Get database statistics"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Total games
            cursor.execute('SELECT COUNT(*) FROM games')
            total_games = cursor.fetchone()[0]
            
            # Total reviews
            cursor.execute('SELECT COUNT(*) FROM reviews')
            total_reviews = cursor.fetchone()[0]
            
            # Average rating
            cursor.execute('SELECT AVG(average_rating) FROM games WHERE total_reviews > 0')
            avg_rating = cursor.fetchone()[0] or 0.0
            
            # Most popular genre
            cursor.execute('''
                SELECT genre, COUNT(*) as count 
                FROM games 
                GROUP BY genre 
                ORDER BY count DESC 
                LIMIT 1
            ''')
            popular_genre_result = cursor.fetchone()
            popular_genre = popular_genre_result[0] if popular_genre_result else "Unknown"
            
            return {
                'total_games': total_games,
                'total_reviews': total_reviews,
                'average_rating': round(avg_rating, 1),
                'popular_genre': popular_genre
            }
    
    def get_all_genres(self) -> List[str]:
        """Get all unique genres"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT DISTINCT genre FROM games WHERE genre IS NOT NULL ORDER BY genre')
            return [row[0] for row in cursor.fetchall()]
    
    def close(self):
        """Close database connection (SQLite handles this automatically)"""
        pass

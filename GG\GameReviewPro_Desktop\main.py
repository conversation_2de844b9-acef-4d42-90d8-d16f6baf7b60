"""
GameReview Pro - Windows 11 Native Desktop Application
Main application entry point with CustomTkinter GUI
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
import sys
from typing import List, Dict, Optional
from datetime import datetime

# Import our modules
from database import GameReviewDatabase
from sample_data import load_sample_data

# Configure CustomTkinter
ctk.set_appearance_mode("dark")  # "dark" or "light"
ctk.set_default_color_theme("blue")  # "blue", "green", "dark-blue"

class GameReviewProApp:
    """Main application class for GameReview Pro Desktop"""

    def __init__(self):
        """Initialize the application"""
        print("🎮 GameReview Pro - Windows 11 Desktop Application")
        print("=" * 60)

        # Initialize database
        self.db = GameReviewDatabase()

        # Load sample data if needed
        load_sample_data(self.db)

        # Initialize GUI
        self.init_gui()

        # Load initial data
        self.load_data()

        print("✅ Application initialized successfully!")

    def init_gui(self):
        """Initialize the GUI components"""
        # Create main window
        self.root = ctk.CTk()
        self.root.title("🎮 GameReview Pro - Desktop Edition")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)

        # Configure grid weights
        self.root.grid_columnconfigure(1, weight=1)
        self.root.grid_rowconfigure(0, weight=1)

        # Create main components
        self.create_sidebar()
        self.create_main_content()
        self.create_status_bar()

        # Initialize with dashboard
        self.show_dashboard()

    def create_sidebar(self):
        """Create navigation sidebar"""
        self.sidebar_frame = ctk.CTkFrame(self.root, width=280, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, rowspan=2, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(8, weight=1)

        # Logo/Title
        self.logo_label = ctk.CTkLabel(
            self.sidebar_frame,
            text="🎮 GameReview Pro",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        self.logo_label.grid(row=0, column=0, padx=20, pady=(30, 10))

        # Subtitle
        self.subtitle_label = ctk.CTkLabel(
            self.sidebar_frame,
            text="Desktop Edition",
            font=ctk.CTkFont(size=14)
        )
        self.subtitle_label.grid(row=1, column=0, padx=20, pady=(0, 30))

        # Navigation buttons
        self.nav_buttons = {}
        nav_items = [
            ("🏠 Dashboard", self.show_dashboard),
            ("🎮 Browse Games", self.show_games),
            ("📝 Reviews", self.show_reviews),
            ("✍️ Write Review", self.show_write_review),
            ("🔍 Search", self.show_search),
            ("📊 Statistics", self.show_statistics)
        ]

        for i, (text, command) in enumerate(nav_items, 2):
            btn = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                command=command,
                height=45,
                font=ctk.CTkFont(size=16),
                anchor="w"
            )
            btn.grid(row=i, column=0, padx=20, pady=8, sticky="ew")
            self.nav_buttons[text] = btn

        # App info
        info_frame = ctk.CTkFrame(self.sidebar_frame)
        info_frame.grid(row=9, column=0, padx=20, pady=20, sticky="ew")

        ctk.CTkLabel(
            info_frame,
            text="Windows 11 Native App",
            font=ctk.CTkFont(size=12, weight="bold")
        ).pack(pady=5)

        ctk.CTkLabel(
            info_frame,
            text="Local SQLite Database",
            font=ctk.CTkFont(size=11)
        ).pack(pady=2)

        ctk.CTkLabel(
            info_frame,
            text="No Internet Required",
            font=ctk.CTkFont(size=11)
        ).pack(pady=2)

    def create_main_content(self):
        """Create main content area"""
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.grid(row=0, column=1, sticky="nsew", padx=(0, 20), pady=20)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)

        # Content will be dynamically created based on navigation

    def create_status_bar(self):
        """Create status bar"""
        self.status_frame = ctk.CTkFrame(self.root, height=40)
        self.status_frame.grid(row=1, column=1, sticky="ew", padx=(0, 20), pady=(0, 20))

        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Ready - GameReview Pro Desktop",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=15, pady=10)

        # Database status
        stats = self.db.get_statistics()
        self.db_status_label = ctk.CTkLabel(
            self.status_frame,
            text=f"📊 {stats['total_games']} Games • {stats['total_reviews']} Reviews",
            font=ctk.CTkFont(size=12)
        )
        self.db_status_label.pack(side="right", padx=15, pady=10)

    def clear_main_content(self):
        """Clear the main content area"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()

    def update_status(self, message: str):
        """Update status bar message"""
        self.status_label.configure(text=message)
        self.root.update_idletasks()

    def load_data(self):
        """Load initial data from database"""
        try:
            self.games_data = self.db.get_all_games()
            self.reviews_data = self.db.get_recent_reviews(20)
            self.genres = self.db.get_all_genres()
            print(f"✅ Loaded {len(self.games_data)} games and {len(self.reviews_data)} reviews")
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            self.games_data = []
            self.reviews_data = []
            self.genres = []

    def show_dashboard(self):
        """Show dashboard view"""
        self.clear_main_content()
        self.update_status("Dashboard loaded")

        # Create scrollable frame
        scrollable_frame = ctk.CTkScrollableFrame(self.main_frame)
        scrollable_frame.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        scrollable_frame.grid_columnconfigure(0, weight=1)

        # Welcome section
        welcome_frame = ctk.CTkFrame(scrollable_frame)
        welcome_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))
        welcome_frame.grid_columnconfigure(0, weight=1)

        ctk.CTkLabel(
            welcome_frame,
            text="Welcome to GameReview Pro",
            font=ctk.CTkFont(size=32, weight="bold")
        ).pack(pady=20)

        ctk.CTkLabel(
            welcome_frame,
            text="Your personal game review platform running natively on Windows 11",
            font=ctk.CTkFont(size=16)
        ).pack(pady=(0, 20))

        # Statistics cards
        self.create_stats_cards(scrollable_frame)

        # Recent reviews section
        self.create_recent_reviews_section(scrollable_frame)

        # Top rated games section
        self.create_top_games_section(scrollable_frame)

    def create_stats_cards(self, parent):
        """Create statistics cards"""
        stats = self.db.get_statistics()

        stats_frame = ctk.CTkFrame(parent)
        stats_frame.grid(row=1, column=0, sticky="ew", pady=(0, 20))
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # Stats data
        stats_data = [
            ("🎮", "Total Games", stats['total_games']),
            ("📝", "Total Reviews", stats['total_reviews']),
            ("⭐", "Average Rating", f"{stats['average_rating']}/5"),
            ("🏆", "Top Genre", stats['popular_genre'])
        ]

        for i, (icon, label, value) in enumerate(stats_data):
            card = ctk.CTkFrame(stats_frame)
            card.grid(row=0, column=i, padx=10, pady=15, sticky="ew")

            ctk.CTkLabel(
                card,
                text=icon,
                font=ctk.CTkFont(size=36)
            ).pack(pady=(15, 5))

            ctk.CTkLabel(
                card,
                text=str(value),
                font=ctk.CTkFont(size=24, weight="bold")
            ).pack(pady=5)

            ctk.CTkLabel(
                card,
                text=label,
                font=ctk.CTkFont(size=14)
            ).pack(pady=(0, 15))

    def create_recent_reviews_section(self, parent):
        """Create recent reviews section"""
        reviews_frame = ctk.CTkFrame(parent)
        reviews_frame.grid(row=2, column=0, sticky="ew", pady=(0, 20))
        reviews_frame.grid_columnconfigure(0, weight=1)

        ctk.CTkLabel(
            reviews_frame,
            text="📝 Recent Reviews",
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(anchor="w", padx=20, pady=(20, 10))

        # Recent reviews list
        recent_reviews = self.db.get_recent_reviews(5)

        for review in recent_reviews:
            review_item = ctk.CTkFrame(reviews_frame)
            review_item.pack(fill="x", padx=20, pady=5)

            # Review header
            header_frame = ctk.CTkFrame(review_item)
            header_frame.pack(fill="x", padx=15, pady=10)

            # Game title and rating
            title_text = f"{review['game_title']} - {'⭐' * review['rating']} ({review['rating']}/5)"
            ctk.CTkLabel(
                header_frame,
                text=title_text,
                font=ctk.CTkFont(size=14, weight="bold")
            ).pack(anchor="w")

            # Reviewer and date
            meta_text = f"By {review['reviewer_name']} • {review['created_at'][:10]}"
            ctk.CTkLabel(
                header_frame,
                text=meta_text,
                font=ctk.CTkFont(size=12)
            ).pack(anchor="w")

            # Review text (truncated)
            if review['review_text']:
                review_text = review['review_text'][:100] + "..." if len(review['review_text']) > 100 else review['review_text']
                ctk.CTkLabel(
                    header_frame,
                    text=review_text,
                    font=ctk.CTkFont(size=11),
                    wraplength=600
                ).pack(anchor="w", pady=(5, 0))

    def create_top_games_section(self, parent):
        """Create top rated games section"""
        games_frame = ctk.CTkFrame(parent)
        games_frame.grid(row=3, column=0, sticky="ew", pady=(0, 20))
        games_frame.grid_columnconfigure(0, weight=1)

        ctk.CTkLabel(
            games_frame,
            text="🏆 Top Rated Games",
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(anchor="w", padx=20, pady=(20, 10))

        # Get top rated games
        top_games = sorted(self.games_data, key=lambda x: (x['average_rating'], x['total_reviews']), reverse=True)[:5]

        for game in top_games:
            game_item = ctk.CTkFrame(games_frame)
            game_item.pack(fill="x", padx=20, pady=5)

            game_info = ctk.CTkFrame(game_item)
            game_info.pack(fill="x", padx=15, pady=10)

            # Game title and rating
            title_text = f"{game['title']} - ⭐ {game['average_rating']}/5 ({game['total_reviews']} reviews)"
            ctk.CTkLabel(
                game_info,
                text=title_text,
                font=ctk.CTkFont(size=14, weight="bold")
            ).pack(anchor="w")

            # Genre and developer
            meta_text = f"{game['genre']} • {game['developer']} • ${game['price']}"
            ctk.CTkLabel(
                game_info,
                text=meta_text,
                font=ctk.CTkFont(size=12)
            ).pack(anchor="w")

            # Description (truncated)
            if game['description']:
                desc_text = game['description'][:120] + "..." if len(game['description']) > 120 else game['description']
                ctk.CTkLabel(
                    game_info,
                    text=desc_text,
                    font=ctk.CTkFont(size=11),
                    wraplength=700
                ).pack(anchor="w", pady=(5, 0))

    def show_games(self):
        """Show games browser view"""
        self.clear_main_content()
        self.update_status("Games browser loaded")

        # Create main container
        container = ctk.CTkFrame(self.main_frame)
        container.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        container.grid_columnconfigure(0, weight=1)
        container.grid_rowconfigure(1, weight=1)

        # Header with search and filter
        header_frame = ctk.CTkFrame(container)
        header_frame.grid(row=0, column=0, sticky="ew", pady=(0, 15))
        header_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(
            header_frame,
            text="🎮 Browse Games",
            font=ctk.CTkFont(size=24, weight="bold")
        ).grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # Search entry
        self.search_var = ctk.StringVar()
        search_entry = ctk.CTkEntry(
            header_frame,
            textvariable=self.search_var,
            placeholder_text="Search games...",
            width=300,
            height=35
        )
        search_entry.grid(row=0, column=1, padx=20, pady=15, sticky="e")
        search_entry.bind("<KeyRelease>", self.on_search_change)

        # Genre filter
        self.genre_var = ctk.StringVar(value="All Genres")
        genre_menu = ctk.CTkOptionMenu(
            header_frame,
            variable=self.genre_var,
            values=["All Genres"] + self.genres,
            command=self.on_genre_change,
            width=150,
            height=35
        )
        genre_menu.grid(row=0, column=2, padx=(10, 20), pady=15)

        # Games grid
        self.games_scrollable = ctk.CTkScrollableFrame(container)
        self.games_scrollable.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))

        # Load games
        self.display_games(self.games_data)

    def display_games(self, games: List[Dict]):
        """Display games in a grid layout"""
        # Clear existing games
        for widget in self.games_scrollable.winfo_children():
            widget.destroy()

        # Display games in rows of 2
        for i, game in enumerate(games):
            row = i // 2
            col = i % 2

            game_card = ctk.CTkFrame(self.games_scrollable)
            game_card.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
            self.games_scrollable.grid_columnconfigure(col, weight=1)

            # Game info
            info_frame = ctk.CTkFrame(game_card)
            info_frame.pack(fill="both", expand=True, padx=15, pady=15)

            # Title
            ctk.CTkLabel(
                info_frame,
                text=game['title'],
                font=ctk.CTkFont(size=18, weight="bold")
            ).pack(anchor="w")

            # Rating and reviews
            rating_text = f"⭐ {game['average_rating']}/5 ({game['total_reviews']} reviews)"
            ctk.CTkLabel(
                info_frame,
                text=rating_text,
                font=ctk.CTkFont(size=14)
            ).pack(anchor="w", pady=(5, 0))

            # Genre and developer
            meta_text = f"{game['genre']} • {game['developer']}"
            ctk.CTkLabel(
                info_frame,
                text=meta_text,
                font=ctk.CTkFont(size=12)
            ).pack(anchor="w", pady=(2, 0))

            # Price
            price_text = f"${game['price']}" if game['price'] > 0 else "Free"
            ctk.CTkLabel(
                info_frame,
                text=price_text,
                font=ctk.CTkFont(size=14, weight="bold")
            ).pack(anchor="w", pady=(5, 0))

            # Description
            desc_text = game['description'][:100] + "..." if len(game['description']) > 100 else game['description']
            ctk.CTkLabel(
                info_frame,
                text=desc_text,
                font=ctk.CTkFont(size=11),
                wraplength=300
            ).pack(anchor="w", pady=(5, 10))

            # View reviews button
            ctk.CTkButton(
                info_frame,
                text="View Reviews",
                command=lambda g=game: self.view_game_reviews(g),
                height=30
            ).pack(anchor="w")

    def on_search_change(self, event=None):
        """Handle search input changes"""
        query = self.search_var.get().strip()
        if query:
            filtered_games = self.db.search_games(query)
        else:
            filtered_games = self.games_data

        # Apply genre filter if set
        genre = self.genre_var.get()
        if genre != "All Genres":
            filtered_games = [g for g in filtered_games if g['genre'] == genre]

        self.display_games(filtered_games)

    def on_genre_change(self, genre: str):
        """Handle genre filter changes"""
        if genre == "All Genres":
            filtered_games = self.games_data
        else:
            filtered_games = self.db.get_games_by_genre(genre)

        # Apply search filter if set
        query = self.search_var.get().strip()
        if query:
            filtered_games = [g for g in filtered_games if
                            query.lower() in g['title'].lower() or
                            query.lower() in g['developer'].lower()]

        self.display_games(filtered_games)

    def view_game_reviews(self, game: Dict):
        """View reviews for a specific game"""
        self.selected_game = game
        self.show_reviews()

    def show_reviews(self):
        """Show reviews view"""
        self.clear_main_content()
        self.update_status("Reviews loaded")

        # Create main container
        container = ctk.CTkFrame(self.main_frame)
        container.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        container.grid_columnconfigure(0, weight=1)
        container.grid_rowconfigure(1, weight=1)

        # Header
        header_frame = ctk.CTkFrame(container)
        header_frame.grid(row=0, column=0, sticky="ew", pady=(0, 15))

        title_text = "📝 All Reviews"
        if hasattr(self, 'selected_game'):
            title_text = f"📝 Reviews for {self.selected_game['title']}"

        ctk.CTkLabel(
            header_frame,
            text=title_text,
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Back button if viewing specific game
        if hasattr(self, 'selected_game'):
            ctk.CTkButton(
                header_frame,
                text="← Back to All Reviews",
                command=lambda: [delattr(self, 'selected_game'), self.show_reviews()],
                height=35
            ).pack(side="right", padx=20, pady=15)

        # Reviews list
        reviews_scrollable = ctk.CTkScrollableFrame(container)
        reviews_scrollable.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))

        # Get reviews
        if hasattr(self, 'selected_game'):
            reviews = self.db.get_reviews_for_game(self.selected_game['id'])
        else:
            reviews = self.db.get_recent_reviews(50)

        # Display reviews
        for review in reviews:
            review_card = ctk.CTkFrame(reviews_scrollable)
            review_card.pack(fill="x", padx=10, pady=10)

            review_info = ctk.CTkFrame(review_card)
            review_info.pack(fill="both", expand=True, padx=15, pady=15)

            # Review header
            header_text = f"{review['game_title']} - {'⭐' * review['rating']} ({review['rating']}/5)"
            ctk.CTkLabel(
                review_info,
                text=header_text,
                font=ctk.CTkFont(size=16, weight="bold")
            ).pack(anchor="w")

            # Reviewer info
            meta_text = f"By {review['reviewer_name']} on {review['platform']} • {review['created_at'][:10]}"
            if review['hours_played'] > 0:
                meta_text += f" • {review['hours_played']} hours played"

            ctk.CTkLabel(
                review_info,
                text=meta_text,
                font=ctk.CTkFont(size=12)
            ).pack(anchor="w", pady=(5, 0))

            # Review text
            if review['review_text']:
                ctk.CTkLabel(
                    review_info,
                    text=review['review_text'],
                    font=ctk.CTkFont(size=11),
                    wraplength=800
                ).pack(anchor="w", pady=(10, 0))

            # Pros and cons
            if review['pros'] or review['cons']:
                pros_cons_frame = ctk.CTkFrame(review_info)
                pros_cons_frame.pack(fill="x", pady=(10, 0))
                pros_cons_frame.grid_columnconfigure((0, 1), weight=1)

                if review['pros']:
                    pros_frame = ctk.CTkFrame(pros_cons_frame)
                    pros_frame.grid(row=0, column=0, padx=(0, 5), pady=10, sticky="ew")

                    ctk.CTkLabel(
                        pros_frame,
                        text="👍 Pros:",
                        font=ctk.CTkFont(size=12, weight="bold")
                    ).pack(anchor="w", padx=10, pady=(10, 5))

                    for pro in review['pros']:
                        ctk.CTkLabel(
                            pros_frame,
                            text=f"• {pro}",
                            font=ctk.CTkFont(size=11)
                        ).pack(anchor="w", padx=20, pady=1)

                if review['cons']:
                    cons_frame = ctk.CTkFrame(pros_cons_frame)
                    cons_frame.grid(row=0, column=1, padx=(5, 0), pady=10, sticky="ew")

                    ctk.CTkLabel(
                        cons_frame,
                        text="👎 Cons:",
                        font=ctk.CTkFont(size=12, weight="bold")
                    ).pack(anchor="w", padx=10, pady=(10, 5))

                    for con in review['cons']:
                        ctk.CTkLabel(
                            cons_frame,
                            text=f"• {con}",
                            font=ctk.CTkFont(size=11)
                        ).pack(anchor="w", padx=20, pady=1)

    def show_write_review(self):
        """Show write review form"""
        self.clear_main_content()
        self.update_status("Write review form loaded")

        # Create scrollable container
        container = ctk.CTkScrollableFrame(self.main_frame)
        container.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        container.grid_columnconfigure(0, weight=1)

        # Header
        ctk.CTkLabel(
            container,
            text="✍️ Write a Review",
            font=ctk.CTkFont(size=28, weight="bold")
        ).pack(pady=(20, 30))

        # Form frame
        form_frame = ctk.CTkFrame(container)
        form_frame.pack(fill="x", padx=50, pady=(0, 30))

        # Game selection
        ctk.CTkLabel(
            form_frame,
            text="Select Game:",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(anchor="w", padx=30, pady=(30, 10))

        game_titles = [game['title'] for game in self.games_data]
        self.selected_game_var = ctk.StringVar()
        game_menu = ctk.CTkOptionMenu(
            form_frame,
            variable=self.selected_game_var,
            values=game_titles,
            width=400,
            height=40
        )
        game_menu.pack(anchor="w", padx=30, pady=(0, 20))

        # Reviewer name
        ctk.CTkLabel(
            form_frame,
            text="Your Name:",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(anchor="w", padx=30, pady=(0, 10))

        self.reviewer_name_var = ctk.StringVar(value="Anonymous")
        name_entry = ctk.CTkEntry(
            form_frame,
            textvariable=self.reviewer_name_var,
            width=400,
            height=40
        )
        name_entry.pack(anchor="w", padx=30, pady=(0, 20))

        # Rating
        ctk.CTkLabel(
            form_frame,
            text="Rating:",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(anchor="w", padx=30, pady=(0, 10))

        self.rating_var = ctk.StringVar(value="5")
        rating_menu = ctk.CTkOptionMenu(
            form_frame,
            variable=self.rating_var,
            values=["1", "2", "3", "4", "5"],
            width=200,
            height=40
        )
        rating_menu.pack(anchor="w", padx=30, pady=(0, 20))

        # Platform
        ctk.CTkLabel(
            form_frame,
            text="Platform:",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(anchor="w", padx=30, pady=(0, 10))

        platforms = ["PC", "PlayStation 5", "PlayStation 4", "Xbox Series X/S", "Xbox One", "Nintendo Switch"]
        self.platform_var = ctk.StringVar(value="PC")
        platform_menu = ctk.CTkOptionMenu(
            form_frame,
            variable=self.platform_var,
            values=platforms,
            width=300,
            height=40
        )
        platform_menu.pack(anchor="w", padx=30, pady=(0, 20))

        # Hours played
        ctk.CTkLabel(
            form_frame,
            text="Hours Played:",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(anchor="w", padx=30, pady=(0, 10))

        self.hours_var = ctk.StringVar(value="0")
        hours_entry = ctk.CTkEntry(
            form_frame,
            textvariable=self.hours_var,
            width=200,
            height=40
        )
        hours_entry.pack(anchor="w", padx=30, pady=(0, 20))

        # Review text
        ctk.CTkLabel(
            form_frame,
            text="Review:",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(anchor="w", padx=30, pady=(0, 10))

        self.review_textbox = ctk.CTkTextbox(
            form_frame,
            width=600,
            height=150
        )
        self.review_textbox.pack(anchor="w", padx=30, pady=(0, 20))

        # Pros
        ctk.CTkLabel(
            form_frame,
            text="Pros (one per line):",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(anchor="w", padx=30, pady=(0, 10))

        self.pros_textbox = ctk.CTkTextbox(
            form_frame,
            width=600,
            height=80
        )
        self.pros_textbox.pack(anchor="w", padx=30, pady=(0, 20))

        # Cons
        ctk.CTkLabel(
            form_frame,
            text="Cons (one per line):",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(anchor="w", padx=30, pady=(0, 10))

        self.cons_textbox = ctk.CTkTextbox(
            form_frame,
            width=600,
            height=80
        )
        self.cons_textbox.pack(anchor="w", padx=30, pady=(0, 30))

        # Submit button
        submit_btn = ctk.CTkButton(
            form_frame,
            text="Submit Review",
            command=self.submit_review,
            width=200,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        submit_btn.pack(anchor="w", padx=30, pady=(0, 30))

    def submit_review(self):
        """Submit the review form"""
        try:
            # Get form data
            game_title = self.selected_game_var.get()
            if not game_title:
                messagebox.showerror("Error", "Please select a game")
                return

            # Find game ID
            game = next((g for g in self.games_data if g['title'] == game_title), None)
            if not game:
                messagebox.showerror("Error", "Selected game not found")
                return

            reviewer_name = self.reviewer_name_var.get().strip() or "Anonymous"
            rating = int(self.rating_var.get())
            platform = self.platform_var.get()

            try:
                hours_played = float(self.hours_var.get()) if self.hours_var.get() else 0.0
            except ValueError:
                hours_played = 0.0

            review_text = self.review_textbox.get("1.0", "end-1c").strip()

            # Parse pros and cons
            pros_text = self.pros_textbox.get("1.0", "end-1c").strip()
            pros = [line.strip() for line in pros_text.split('\n') if line.strip()]

            cons_text = self.cons_textbox.get("1.0", "end-1c").strip()
            cons = [line.strip() for line in cons_text.split('\n') if line.strip()]

            # Submit review
            review_id = self.db.add_review(
                game_id=game['id'],
                reviewer_name=reviewer_name,
                rating=rating,
                review_text=review_text,
                platform=platform,
                hours_played=hours_played,
                pros=pros,
                cons=cons
            )

            if review_id:
                messagebox.showinfo("Success", "Review submitted successfully!")

                # Clear form
                self.selected_game_var.set("")
                self.reviewer_name_var.set("Anonymous")
                self.rating_var.set("5")
                self.platform_var.set("PC")
                self.hours_var.set("0")
                self.review_textbox.delete("1.0", "end")
                self.pros_textbox.delete("1.0", "end")
                self.cons_textbox.delete("1.0", "end")

                # Refresh data
                self.load_data()
                self.update_status("Review submitted successfully")
            else:
                messagebox.showerror("Error", "Failed to submit review")

        except Exception as e:
            messagebox.showerror("Error", f"Error submitting review: {e}")

    def show_search(self):
        """Show advanced search view"""
        self.clear_main_content()
        self.update_status("Advanced search loaded")

        # Create container
        container = ctk.CTkFrame(self.main_frame)
        container.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        container.grid_columnconfigure(0, weight=1)
        container.grid_rowconfigure(1, weight=1)

        # Header
        header_frame = ctk.CTkFrame(container)
        header_frame.grid(row=0, column=0, sticky="ew", pady=(0, 15))

        ctk.CTkLabel(
            header_frame,
            text="🔍 Advanced Search",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Search controls
        controls_frame = ctk.CTkFrame(header_frame)
        controls_frame.pack(side="right", padx=20, pady=15)

        # Search entry
        self.adv_search_var = ctk.StringVar()
        search_entry = ctk.CTkEntry(
            controls_frame,
            textvariable=self.adv_search_var,
            placeholder_text="Search games, developers, genres...",
            width=300,
            height=35
        )
        search_entry.pack(side="left", padx=(0, 10))

        # Search button
        search_btn = ctk.CTkButton(
            controls_frame,
            text="Search",
            command=self.perform_advanced_search,
            height=35,
            width=100
        )
        search_btn.pack(side="left")

        # Results area
        self.search_results_frame = ctk.CTkScrollableFrame(container)
        self.search_results_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))

        # Show initial message
        ctk.CTkLabel(
            self.search_results_frame,
            text="Enter a search term above to find games",
            font=ctk.CTkFont(size=16)
        ).pack(pady=50)

    def perform_advanced_search(self):
        """Perform advanced search"""
        query = self.adv_search_var.get().strip()
        if not query:
            return

        # Clear results
        for widget in self.search_results_frame.winfo_children():
            widget.destroy()

        # Search games
        results = self.db.search_games(query)

        if not results:
            ctk.CTkLabel(
                self.search_results_frame,
                text=f"No results found for '{query}'",
                font=ctk.CTkFont(size=16)
            ).pack(pady=50)
            return

        # Display results
        ctk.CTkLabel(
            self.search_results_frame,
            text=f"Found {len(results)} results for '{query}'",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(anchor="w", padx=20, pady=(20, 10))

        self.display_games_in_search(results)

    def display_games_in_search(self, games: List[Dict]):
        """Display games in search results"""
        for game in games:
            game_card = ctk.CTkFrame(self.search_results_frame)
            game_card.pack(fill="x", padx=20, pady=10)

            game_info = ctk.CTkFrame(game_card)
            game_info.pack(fill="both", expand=True, padx=15, pady=15)
            game_info.grid_columnconfigure(0, weight=1)

            # Title and rating
            title_frame = ctk.CTkFrame(game_info)
            title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
            title_frame.grid_columnconfigure(0, weight=1)

            ctk.CTkLabel(
                title_frame,
                text=game['title'],
                font=ctk.CTkFont(size=20, weight="bold")
            ).pack(side="left", padx=15, pady=10)

            rating_text = f"⭐ {game['average_rating']}/5 ({game['total_reviews']} reviews)"
            ctk.CTkLabel(
                title_frame,
                text=rating_text,
                font=ctk.CTkFont(size=14)
            ).pack(side="right", padx=15, pady=10)

            # Game details
            details_text = f"{game['genre']} • {game['developer']} • ${game['price']}"
            ctk.CTkLabel(
                game_info,
                text=details_text,
                font=ctk.CTkFont(size=12)
            ).grid(row=1, column=0, sticky="w", padx=15, pady=(0, 10))

            # Description
            ctk.CTkLabel(
                game_info,
                text=game['description'],
                font=ctk.CTkFont(size=11),
                wraplength=800
            ).grid(row=2, column=0, sticky="w", padx=15, pady=(0, 15))

            # Action buttons
            buttons_frame = ctk.CTkFrame(game_info)
            buttons_frame.grid(row=3, column=0, sticky="w", padx=15, pady=(0, 15))

            ctk.CTkButton(
                buttons_frame,
                text="View Reviews",
                command=lambda g=game: self.view_game_reviews(g),
                height=30,
                width=120
            ).pack(side="left", padx=(0, 10))

    def show_statistics(self):
        """Show statistics view"""
        self.clear_main_content()
        self.update_status("Statistics loaded")

        # Create scrollable container
        container = ctk.CTkScrollableFrame(self.main_frame)
        container.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        container.grid_columnconfigure(0, weight=1)

        # Header
        ctk.CTkLabel(
            container,
            text="📊 Statistics & Analytics",
            font=ctk.CTkFont(size=28, weight="bold")
        ).pack(pady=(20, 30))

        # Get statistics
        stats = self.db.get_statistics()

        # Overview stats
        overview_frame = ctk.CTkFrame(container)
        overview_frame.pack(fill="x", padx=20, pady=(0, 30))
        overview_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        stats_data = [
            ("🎮", "Total Games", stats['total_games']),
            ("📝", "Total Reviews", stats['total_reviews']),
            ("⭐", "Average Rating", f"{stats['average_rating']}/5"),
            ("🏆", "Top Genre", stats['popular_genre'])
        ]

        for i, (icon, label, value) in enumerate(stats_data):
            stat_card = ctk.CTkFrame(overview_frame)
            stat_card.grid(row=0, column=i, padx=10, pady=20, sticky="ew")

            ctk.CTkLabel(
                stat_card,
                text=icon,
                font=ctk.CTkFont(size=48)
            ).pack(pady=(20, 10))

            ctk.CTkLabel(
                stat_card,
                text=str(value),
                font=ctk.CTkFont(size=28, weight="bold")
            ).pack(pady=5)

            ctk.CTkLabel(
                stat_card,
                text=label,
                font=ctk.CTkFont(size=16)
            ).pack(pady=(0, 20))

        # Genre breakdown
        genre_frame = ctk.CTkFrame(container)
        genre_frame.pack(fill="x", padx=20, pady=(0, 30))

        ctk.CTkLabel(
            genre_frame,
            text="📈 Games by Genre",
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(anchor="w", padx=20, pady=(20, 15))

        # Calculate genre statistics
        genre_stats = {}
        for game in self.games_data:
            genre = game['genre']
            if genre not in genre_stats:
                genre_stats[genre] = {'count': 0, 'avg_rating': 0, 'total_rating': 0}
            genre_stats[genre]['count'] += 1
            genre_stats[genre]['total_rating'] += game['average_rating']

        for genre in genre_stats:
            if genre_stats[genre]['count'] > 0:
                genre_stats[genre]['avg_rating'] = genre_stats[genre]['total_rating'] / genre_stats[genre]['count']

        # Display genre stats
        for genre, data in sorted(genre_stats.items(), key=lambda x: x[1]['count'], reverse=True):
            genre_item = ctk.CTkFrame(genre_frame)
            genre_item.pack(fill="x", padx=20, pady=5)

            genre_info = ctk.CTkFrame(genre_item)
            genre_info.pack(fill="x", padx=15, pady=10)
            genre_info.grid_columnconfigure(0, weight=1)

            # Genre name and count
            ctk.CTkLabel(
                genre_info,
                text=f"{genre} ({data['count']} games)",
                font=ctk.CTkFont(size=16, weight="bold")
            ).grid(row=0, column=0, sticky="w")

            # Average rating
            ctk.CTkLabel(
                genre_info,
                text=f"Average Rating: {data['avg_rating']:.1f}/5",
                font=ctk.CTkFont(size=14)
            ).grid(row=0, column=1, sticky="e")

        # Top rated games
        top_games_frame = ctk.CTkFrame(container)
        top_games_frame.pack(fill="x", padx=20, pady=(0, 30))

        ctk.CTkLabel(
            top_games_frame,
            text="🏆 Top Rated Games",
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(anchor="w", padx=20, pady=(20, 15))

        top_games = sorted(self.games_data, key=lambda x: (x['average_rating'], x['total_reviews']), reverse=True)[:10]

        for i, game in enumerate(top_games, 1):
            game_item = ctk.CTkFrame(top_games_frame)
            game_item.pack(fill="x", padx=20, pady=5)

            game_info = ctk.CTkFrame(game_item)
            game_info.pack(fill="x", padx=15, pady=10)
            game_info.grid_columnconfigure(1, weight=1)

            # Rank
            ctk.CTkLabel(
                game_info,
                text=f"#{i}",
                font=ctk.CTkFont(size=16, weight="bold")
            ).grid(row=0, column=0, padx=(0, 15))

            # Game title
            ctk.CTkLabel(
                game_info,
                text=game['title'],
                font=ctk.CTkFont(size=16, weight="bold")
            ).grid(row=0, column=1, sticky="w")

            # Rating
            rating_text = f"⭐ {game['average_rating']}/5 ({game['total_reviews']} reviews)"
            ctk.CTkLabel(
                game_info,
                text=rating_text,
                font=ctk.CTkFont(size=14)
            ).grid(row=0, column=2, sticky="e")

    def run(self):
        """Start the application"""
        print("🚀 Starting GameReview Pro Desktop Application...")
        self.root.mainloop()
        print("👋 Application closed")

def main():
    """Main entry point"""
    try:
        # Check if we're running on Windows
        if os.name != 'nt':
            print("⚠️ This application is optimized for Windows 11")

        # Create and run application
        app = GameReviewProApp()
        app.run()

    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("📦 Please install required packages:")
        print("   pip install customtkinter pillow")
        input("Press Enter to exit...")
    except Exception as e:
        print(f"❌ Application error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
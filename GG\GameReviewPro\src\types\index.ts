// Professional GameReview App - Type Definitions

export interface Game {
  id: number;
  title: string;
  description: string;
  genre: string;
  platforms: string[];
  cover_image: string;
  average_rating: number;
  total_reviews: number;
  created_at: string;
}

export interface Review {
  id: number;
  game_id: number;
  reviewer_name: string;
  rating: number;
  review_text: string;
  platform: string;
  pros: string[];
  cons: string[];
  image_path?: string;
  likes_count: number;
  views_count: number;
  created_at: string;
  game_title?: string;
  game_cover?: string;
}

export interface CreateReviewRequest {
  game_id: number;
  reviewer_name?: string;
  rating: number;
  review_text?: string;
  platform?: string;
  pros?: string[];
  cons?: string[];
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export interface GamesResponse {
  success: boolean;
  games: Game[];
  pagination: PaginationInfo;
}

export interface ReviewsResponse {
  success: boolean;
  reviews: Review[];
}

export interface AppStats {
  total_games: number;
  total_reviews: number;
  overall_average_rating: number;
}

export interface NavigationProps {
  navigation: any;
  route?: any;
}

export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    accent: string;
    success: string;
    warning: string;
    error: string;
    border: string;
    shadow: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  typography: {
    h1: number;
    h2: number;
    h3: number;
    body: number;
    caption: number;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
}

export type RootStackParamList = {
  MainTabs: undefined;
  GameDetail: { gameId: number };
  ReviewDetail: { reviewId: number };
  WriteReview: { gameId: number };
  SearchResults: { query: string };
};

export type MainTabParamList = {
  Home: undefined;
  Discover: undefined;
  WriteReview: undefined;
  Profile: undefined;
};

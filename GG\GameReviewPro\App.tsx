// Professional GameReview Pro - Main App Component
import React from 'react';
import { StatusBar } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';

// Screens
import HomeScreen from './src/screens/HomeScreen';
import DiscoverScreen from './src/screens/DiscoverScreen';
import WriteReviewScreen from './src/screens/WriteReviewScreen';
import ProfileScreen from './src/screens/ProfileScreen';
import GameDetailScreen from './src/screens/GameDetailScreen';
import ReviewDetailScreen from './src/screens/ReviewDetailScreen';

// Types
import { RootStackParamList, MainTabParamList } from './src/types';

// Theme
import { colors, typography } from './src/theme';

const Tab = createBottomTabNavigator<MainTabParamList>();
const Stack = createStackNavigator<RootStackParamList>();

// Tab Navigator
const MainTabs = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Discover':
              iconName = 'explore';
              break;
            case 'WriteReview':
              iconName = 'rate-review';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'home';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primaryBlue,
        tabBarInactiveTintColor: colors.lightGray,
        tabBarStyle: {
          backgroundColor: colors.darkGray,
          borderTopColor: colors.mediumGray,
          borderTopWidth: 1,
          height: 80,
          paddingBottom: 20,
          paddingTop: 10,
        },
        tabBarLabelStyle: {
          ...typography.caption,
          fontWeight: '500',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{ tabBarLabel: 'Home' }}
      />
      <Tab.Screen 
        name="Discover" 
        component={DiscoverScreen}
        options={{ tabBarLabel: 'Discover' }}
      />
      <Tab.Screen 
        name="WriteReview" 
        component={WriteReviewScreen}
        options={{ tabBarLabel: 'Write' }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ tabBarLabel: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

// Main App Component
const App: React.FC = () => {
  return (
    <SafeAreaProvider>
      <StatusBar
        barStyle="light-content"
        backgroundColor={colors.black}
        translucent={false}
      />
      
      <NavigationContainer
        theme={{
          dark: true,
          colors: {
            primary: colors.primaryBlue,
            background: colors.black,
            card: colors.darkGray,
            text: colors.white,
            border: colors.mediumGray,
            notification: colors.primaryBlue,
          },
        }}
      >
        <Stack.Navigator
          screenOptions={{
            headerStyle: {
              backgroundColor: colors.darkGray,
              borderBottomColor: colors.mediumGray,
              borderBottomWidth: 1,
            },
            headerTintColor: colors.white,
            headerTitleStyle: {
              ...typography.h3,
              fontWeight: '600',
            },
            headerBackTitleVisible: false,
            cardStyle: {
              backgroundColor: colors.black,
            },
          }}
        >
          <Stack.Screen
            name="MainTabs"
            component={MainTabs}
            options={{ headerShown: false }}
          />
          
          <Stack.Screen
            name="GameDetail"
            component={GameDetailScreen}
            options={({ route }) => ({
              title: 'Game Details',
              headerShown: true,
            })}
          />
          
          <Stack.Screen
            name="ReviewDetail"
            component={ReviewDetailScreen}
            options={{
              title: 'Review Details',
              headerShown: true,
            }}
          />
        </Stack.Navigator>
      </NavigationContainer>

      {/* Toast Messages */}
      <Toast />
    </SafeAreaProvider>
  );
};

export default App;

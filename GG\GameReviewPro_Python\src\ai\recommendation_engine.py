"""
AI Recommendation Engine for GameReview Pro
Using PyTorch for collaborative filtering and content-based recommendations
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import PCA
import json
import logging
from typing import List, Dict, Tuple

class GameRecommendationEngine:
    """Advanced game recommendation system using multiple approaches"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔥 Recommendation Engine using: {self.device}")
        
        # Models
        self.collaborative_model = None
        self.content_vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        self.game_features = None
        self.user_profiles = {}
        
        # Gaming genre weights
        self.genre_weights = {
            'Action': 1.0, 'Adventure': 0.9, 'RPG': 1.1, 'Strategy': 0.8,
            'Sports': 0.7, 'Racing': 0.6, 'Simulation': 0.7, 'Puzzle': 0.6,
            'Fighting': 0.8, 'Shooter': 0.9, 'Horror': 0.8, 'Platformer': 0.7,
            'MMORPG': 1.0, 'Battle Royale': 0.9, 'Indie': 0.8
        }
        
        print("✅ Recommendation Engine initialized!")
    
    def build_user_profile(self, user_reviews: List[Dict]) -> Dict:
        """Build comprehensive user gaming profile"""
        if not user_reviews:
            return self.get_default_profile()
        
        profile = {
            'preferred_genres': {},
            'preferred_platforms': {},
            'rating_patterns': {},
            'sentiment_preferences': {},
            'gaming_style': 'balanced'
        }
        
        # Analyze genre preferences
        genre_ratings = {}
        platform_ratings = {}
        total_ratings = []
        sentiment_scores = []
        
        for review in user_reviews:
            rating = review.get('rating', 3)
            genre = review.get('game_genre', 'Unknown')
            platform = review.get('platform', 'Unknown')
            sentiment = review.get('sentiment_score', 0.0)
            
            total_ratings.append(rating)
            sentiment_scores.append(sentiment)
            
            # Genre preferences
            if genre not in genre_ratings:
                genre_ratings[genre] = []
            genre_ratings[genre].append(rating)
            
            # Platform preferences
            if platform not in platform_ratings:
                platform_ratings[platform] = []
            platform_ratings[platform].append(rating)
        
        # Calculate preferences
        for genre, ratings in genre_ratings.items():
            avg_rating = np.mean(ratings)
            preference_score = (avg_rating - 3.0) / 2.0  # Normalize to -1 to 1
            profile['preferred_genres'][genre] = float(preference_score)
        
        for platform, ratings in platform_ratings.items():
            avg_rating = np.mean(ratings)
            preference_score = (avg_rating - 3.0) / 2.0
            profile['preferred_platforms'][platform] = float(preference_score)
        
        # Rating patterns
        if total_ratings:
            profile['rating_patterns'] = {
                'average_rating': float(np.mean(total_ratings)),
                'rating_variance': float(np.var(total_ratings)),
                'harsh_critic': np.mean(total_ratings) < 3.0,
                'easy_to_please': np.mean(total_ratings) > 4.0
            }
        
        # Sentiment preferences
        if sentiment_scores:
            profile['sentiment_preferences'] = {
                'average_sentiment': float(np.mean(sentiment_scores)),
                'sentiment_variance': float(np.var(sentiment_scores))
            }
        
        # Determine gaming style
        profile['gaming_style'] = self.determine_gaming_style(profile)
        
        return profile
    
    def determine_gaming_style(self, profile: Dict) -> str:
        """Determine user's gaming style based on preferences"""
        genres = profile.get('preferred_genres', {})
        rating_patterns = profile.get('rating_patterns', {})
        
        # Analyze genre preferences
        action_score = genres.get('Action', 0) + genres.get('Shooter', 0)
        story_score = genres.get('RPG', 0) + genres.get('Adventure', 0)
        casual_score = genres.get('Puzzle', 0) + genres.get('Simulation', 0)
        competitive_score = genres.get('Battle Royale', 0) + genres.get('Fighting', 0)
        
        # Determine style
        if action_score > 0.3:
            return 'action_lover'
        elif story_score > 0.3:
            return 'story_focused'
        elif competitive_score > 0.3:
            return 'competitive'
        elif casual_score > 0.3:
            return 'casual'
        elif rating_patterns.get('harsh_critic', False):
            return 'critic'
        else:
            return 'balanced'
    
    def get_content_based_recommendations(self, user_profile: Dict, games: List[Dict], 
                                        num_recommendations: int = 10) -> List[Dict]:
        """Content-based recommendations using game features"""
        if not games:
            return []
        
        try:
            # Create game feature matrix
            game_texts = []
            game_data = []
            
            for game in games:
                # Combine text features
                text_features = f"{game.get('genre', '')} {game.get('description', '')} {game.get('developer', '')}"
                game_texts.append(text_features)
                game_data.append(game)
            
            # Vectorize text features
            if len(game_texts) > 1:
                tfidf_matrix = self.content_vectorizer.fit_transform(game_texts)
            else:
                return games[:num_recommendations]
            
            # Calculate user preference vector
            user_vector = self.create_user_preference_vector(user_profile, games)
            
            # Calculate similarities
            similarities = cosine_similarity(user_vector.reshape(1, -1), tfidf_matrix)[0]
            
            # Get top recommendations
            game_scores = list(zip(game_data, similarities))
            game_scores.sort(key=lambda x: x[1], reverse=True)
            
            recommendations = []
            for game, score in game_scores[:num_recommendations]:
                rec = game.copy()
                rec['recommendation_score'] = float(score)
                rec['recommendation_reason'] = self.generate_recommendation_reason(game, user_profile)
                recommendations.append(rec)
            
            return recommendations
            
        except Exception as e:
            logging.error(f"Content-based recommendation error: {e}")
            return games[:num_recommendations]
    
    def create_user_preference_vector(self, user_profile: Dict, games: List[Dict]) -> np.ndarray:
        """Create user preference vector for content-based filtering"""
        # Get user's preferred genres
        preferred_genres = user_profile.get('preferred_genres', {})
        
        # Find a representative game based on preferences
        best_genre = max(preferred_genres.items(), key=lambda x: x[1])[0] if preferred_genres else 'Action'
        
        # Create preference text
        preference_text = f"{best_genre} " * 3  # Weight preferred genre
        
        # Add other preferences
        for genre, score in preferred_genres.items():
            if score > 0:
                preference_text += f"{genre} " * int(score * 2 + 1)
        
        # Vectorize preference
        try:
            preference_vector = self.content_vectorizer.transform([preference_text])
            return preference_vector.toarray()[0]
        except:
            # Fallback: return zero vector
            return np.zeros(self.content_vectorizer.get_feature_names_out().shape[0])
    
    def get_collaborative_recommendations(self, user_id: int, user_reviews: List[Dict], 
                                        all_reviews: List[Dict], games: List[Dict],
                                        num_recommendations: int = 10) -> List[Dict]:
        """Collaborative filtering recommendations"""
        try:
            # Create user-item matrix
            user_item_matrix = self.create_user_item_matrix(all_reviews)
            
            if user_item_matrix.shape[0] < 2:  # Need at least 2 users
                return []
            
            # Find similar users
            similar_users = self.find_similar_users(user_id, user_item_matrix)
            
            # Get recommendations from similar users
            recommendations = self.get_recommendations_from_similar_users(
                user_id, similar_users, all_reviews, games, num_recommendations
            )
            
            return recommendations
            
        except Exception as e:
            logging.error(f"Collaborative filtering error: {e}")
            return []
    
    def create_user_item_matrix(self, reviews: List[Dict]) -> np.ndarray:
        """Create user-item rating matrix"""
        # Get unique users and games
        users = list(set(review.get('user_id', 0) for review in reviews))
        games = list(set(review.get('game_id', 0) for review in reviews))
        
        # Create matrix
        matrix = np.zeros((len(users), len(games)))
        
        user_to_idx = {user: idx for idx, user in enumerate(users)}
        game_to_idx = {game: idx for idx, game in enumerate(games)}
        
        for review in reviews:
            user_idx = user_to_idx.get(review.get('user_id', 0), 0)
            game_idx = game_to_idx.get(review.get('game_id', 0), 0)
            rating = review.get('rating', 3)
            matrix[user_idx, game_idx] = rating
        
        return matrix
    
    def find_similar_users(self, user_id: int, user_item_matrix: np.ndarray) -> List[int]:
        """Find users with similar preferences"""
        if user_item_matrix.shape[0] < 2:
            return []
        
        # Calculate user similarities using cosine similarity
        similarities = cosine_similarity(user_item_matrix)
        
        # Get similar users (excluding self)
        user_similarities = similarities[0]  # Assuming user_id maps to index 0
        similar_indices = np.argsort(user_similarities)[::-1][1:6]  # Top 5 similar users
        
        return similar_indices.tolist()
    
    def get_recommendations_from_similar_users(self, user_id: int, similar_users: List[int],
                                             all_reviews: List[Dict], games: List[Dict],
                                             num_recommendations: int) -> List[Dict]:
        """Get game recommendations from similar users"""
        # Get games rated highly by similar users
        similar_user_games = {}
        
        for review in all_reviews:
            reviewer_id = review.get('user_id', 0)
            if reviewer_id in similar_users and review.get('rating', 0) >= 4:
                game_id = review.get('game_id', 0)
                if game_id not in similar_user_games:
                    similar_user_games[game_id] = []
                similar_user_games[game_id].append(review.get('rating', 0))
        
        # Calculate average ratings
        game_scores = {}
        for game_id, ratings in similar_user_games.items():
            game_scores[game_id] = np.mean(ratings)
        
        # Sort by score
        sorted_games = sorted(game_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Get game details
        recommendations = []
        game_dict = {game['id']: game for game in games}
        
        for game_id, score in sorted_games[:num_recommendations]:
            if game_id in game_dict:
                rec = game_dict[game_id].copy()
                rec['recommendation_score'] = float(score)
                rec['recommendation_reason'] = f"Users with similar taste rated this {score:.1f}/5"
                recommendations.append(rec)
        
        return recommendations
    
    def get_hybrid_recommendations(self, user_id: int, user_reviews: List[Dict],
                                 all_reviews: List[Dict], games: List[Dict],
                                 num_recommendations: int = 10) -> List[Dict]:
        """Hybrid recommendations combining multiple approaches"""
        # Build user profile
        user_profile = self.build_user_profile(user_reviews)
        
        # Get content-based recommendations
        content_recs = self.get_content_based_recommendations(
            user_profile, games, num_recommendations * 2
        )
        
        # Get collaborative recommendations
        collab_recs = self.get_collaborative_recommendations(
            user_id, user_reviews, all_reviews, games, num_recommendations
        )
        
        # Combine recommendations
        combined_recs = {}
        
        # Add content-based (weight: 0.6)
        for rec in content_recs:
            game_id = rec['id']
            combined_recs[game_id] = {
                'game': rec,
                'content_score': rec.get('recommendation_score', 0.0) * 0.6,
                'collab_score': 0.0
            }
        
        # Add collaborative (weight: 0.4)
        for rec in collab_recs:
            game_id = rec['id']
            if game_id in combined_recs:
                combined_recs[game_id]['collab_score'] = rec.get('recommendation_score', 0.0) * 0.4
            else:
                combined_recs[game_id] = {
                    'game': rec,
                    'content_score': 0.0,
                    'collab_score': rec.get('recommendation_score', 0.0) * 0.4
                }
        
        # Calculate final scores
        final_recommendations = []
        for game_id, data in combined_recs.items():
            final_score = data['content_score'] + data['collab_score']
            game = data['game'].copy()
            game['recommendation_score'] = final_score
            game['recommendation_type'] = 'hybrid'
            final_recommendations.append(game)
        
        # Sort by final score
        final_recommendations.sort(key=lambda x: x['recommendation_score'], reverse=True)
        
        return final_recommendations[:num_recommendations]
    
    def generate_recommendation_reason(self, game: Dict, user_profile: Dict) -> str:
        """Generate human-readable recommendation reason"""
        reasons = []
        
        # Genre match
        game_genre = game.get('genre', '')
        preferred_genres = user_profile.get('preferred_genres', {})
        if game_genre in preferred_genres and preferred_genres[game_genre] > 0.2:
            reasons.append(f"You enjoy {game_genre} games")
        
        # Rating pattern
        rating_patterns = user_profile.get('rating_patterns', {})
        if rating_patterns.get('average_rating', 3.0) > 4.0:
            reasons.append("Highly rated game that matches your standards")
        
        # Gaming style
        gaming_style = user_profile.get('gaming_style', 'balanced')
        style_reasons = {
            'action_lover': "Fast-paced action gameplay",
            'story_focused': "Rich narrative and character development",
            'competitive': "Competitive multiplayer features",
            'casual': "Relaxing and accessible gameplay",
            'critic': "Critically acclaimed and well-designed"
        }
        
        if gaming_style in style_reasons:
            reasons.append(style_reasons[gaming_style])
        
        return "; ".join(reasons) if reasons else "Recommended based on your gaming preferences"
    
    def get_default_profile(self) -> Dict:
        """Default user profile for new users"""
        return {
            'preferred_genres': {'Action': 0.1, 'Adventure': 0.1, 'RPG': 0.1},
            'preferred_platforms': {'PC': 0.1},
            'rating_patterns': {'average_rating': 3.5, 'rating_variance': 1.0},
            'sentiment_preferences': {'average_sentiment': 0.0},
            'gaming_style': 'balanced'
        }

# Global recommendation engine instance
recommendation_engine = GameRecommendationEngine()

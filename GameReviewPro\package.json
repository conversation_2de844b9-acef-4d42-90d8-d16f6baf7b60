{"name": "GameReviewPro", "version": "1.0.0", "description": "Professional Game Review Mobile Application", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace GameReviewPro.xcworkspace -scheme GameReviewPro -configuration Release -destination generic/platform=iOS -archivePath GameReviewPro.xcarchive archive"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/stack": "^6.3.20", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-vector-icons": "^10.0.2", "react-native-image-picker": "^7.0.3", "react-native-star-rating-widget": "^1.7.0", "react-native-linear-gradient": "^2.8.3", "axios": "^1.6.0", "react-native-fast-image": "^8.6.3", "react-native-modal": "^13.0.1", "react-native-toast-message": "^2.1.6", "react-native-skeleton-placeholder": "^5.2.4", "@react-native-async-storage/async-storage": "^1.19.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "mobile", "game-review", "professional", "ios", "android"], "author": "GameReview Team", "license": "MIT"}
#!/usr/bin/env python3
"""
Test script for GameReview Pro Desktop Application
Verifies that the Windows 11 native app works correctly
"""

import sys
import os
import sqlite3

def test_desktop_app():
    """Test the desktop application components"""
    print("🎮 GameReview Pro Desktop Application Test")
    print("=" * 60)
    
    # Test 1: Check Python version
    print("🐍 Testing Python version...")
    if sys.version_info >= (3, 8):
        print(f"✅ Python {sys.version.split()[0]} - Compatible")
    else:
        print(f"❌ Python {sys.version.split()[0]} - Requires 3.8+")
        return False
    
    # Test 2: Check if we can import required modules
    print("\n📦 Testing core dependencies...")
    try:
        import tkinter
        print("✅ tkinter - Available")
    except ImportError:
        print("❌ tkinter - Not available")
        return False
    
    try:
        import sqlite3
        print("✅ sqlite3 - Available")
    except ImportError:
        print("❌ sqlite3 - Not available")
        return False
    
    try:
        import json
        print("✅ json - Available")
    except ImportError:
        print("❌ json - Not available")
        return False
    
    # Test 3: Test database functionality
    print("\n🗄️ Testing database functionality...")
    try:
        # Add the GameReviewPro_Desktop directory to path
        desktop_path = os.path.join(os.path.dirname(__file__), 'GameReviewPro_Desktop')
        sys.path.insert(0, desktop_path)
        
        from database import GameReviewDatabase
        
        # Create test database
        test_db = GameReviewDatabase("test_app.db")
        
        # Test adding a game
        game_id = test_db.add_game(
            title="Test Game",
            description="A test game for verification",
            genre="Test",
            platforms=["PC"],
            developer="Test Studio",
            price=9.99
        )
        
        # Test adding a review
        review_id = test_db.add_review(
            game_id=game_id,
            reviewer_name="Test User",
            rating=5,
            review_text="Great test game!",
            platform="PC",
            pros=["Good for testing"],
            cons=[]
        )
        
        # Test getting data
        games = test_db.get_all_games()
        reviews = test_db.get_recent_reviews()
        stats = test_db.get_statistics()
        
        print(f"✅ Database test passed:")
        print(f"   - Created game ID: {game_id}")
        print(f"   - Created review ID: {review_id}")
        print(f"   - Games in DB: {len(games)}")
        print(f"   - Reviews in DB: {len(reviews)}")
        print(f"   - Stats: {stats}")
        
        # Clean up test database
        if os.path.exists("test_app.db"):
            os.remove("test_app.db")
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False
    
    # Test 4: Test sample data loading
    print("\n📊 Testing sample data...")
    try:
        from sample_data import load_sample_data
        
        # Create test database with sample data
        test_db = GameReviewDatabase("test_sample.db")
        load_sample_data(test_db)
        
        stats = test_db.get_statistics()
        print(f"✅ Sample data loaded:")
        print(f"   - Games: {stats['total_games']}")
        print(f"   - Reviews: {stats['total_reviews']}")
        print(f"   - Average rating: {stats['average_rating']}")
        print(f"   - Popular genre: {stats['popular_genre']}")
        
        # Clean up
        if os.path.exists("test_sample.db"):
            os.remove("test_sample.db")
        
    except Exception as e:
        print(f"❌ Sample data test failed: {e}")
        return False
    
    # Test 5: Test GUI components (basic)
    print("\n🖥️ Testing GUI components...")
    try:
        import tkinter as tk
        
        # Create a test window (don't show it)
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Test basic widgets
        label = tk.Label(root, text="Test")
        button = tk.Button(root, text="Test")
        entry = tk.Entry(root)
        
        # Clean up
        root.destroy()
        
        print("✅ Basic GUI components work")
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        return False
    
    # Test 6: Check CustomTkinter availability
    print("\n🎨 Testing CustomTkinter...")
    try:
        import customtkinter as ctk
        print("✅ CustomTkinter available - Modern GUI ready")
    except ImportError:
        print("⚠️ CustomTkinter not available - Install with: pip install customtkinter")
        print("   Application will fall back to standard tkinter")
    
    print("\n" + "=" * 60)
    print("🎉 All core tests passed!")
    print("✅ GameReview Pro Desktop Application is ready to run")
    print("\n🚀 To start the application:")
    print("   1. cd GameReviewPro_Desktop")
    print("   2. python main.py")
    print("   OR")
    print("   3. Double-click run.bat (Windows)")
    
    return True

def test_windows_compatibility():
    """Test Windows-specific features"""
    print("\n🪟 Testing Windows compatibility...")
    
    if os.name == 'nt':
        print("✅ Running on Windows")
        
        # Test Windows-specific paths
        try:
            import os
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
            print(f"✅ Desktop path: {desktop}")
        except Exception as e:
            print(f"⚠️ Desktop path issue: {e}")
        
        return True
    else:
        print("⚠️ Not running on Windows - application optimized for Windows 11")
        return True

def main():
    """Main test function"""
    try:
        success = test_desktop_app()
        test_windows_compatibility()
        
        if success:
            print("\n🎮 GameReview Pro Desktop Application - Test Summary")
            print("=" * 60)
            print("✅ All systems operational")
            print("✅ Database functionality verified")
            print("✅ GUI components working")
            print("✅ Sample data loading successful")
            print("✅ Ready for Windows 11 deployment")
            print("\n🚀 The application is ready to launch!")
        else:
            print("\n❌ Some tests failed - check output above")
            
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")

if __name__ == "__main__":
    main()

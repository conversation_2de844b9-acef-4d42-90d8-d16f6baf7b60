"""
Review model for GameReview Application
"""

from datetime import datetime
from typing import List, Dict, Optional
from ..database import execute_query, execute_insert, execute_update, update_game_rating

class Review:
    """Review model for managing review data"""
    
    def __init__(self, id=None, game_id=None, reviewer_name='Anonymous', 
                 rating=None, review_text=None, platform=None, pros=None, 
                 cons=None, image_path=None, likes_count=0, views_count=0, 
                 created_at=None):
        self.id = id
        self.game_id = game_id
        self.reviewer_name = reviewer_name or 'Anonymous'
        self.rating = rating
        self.review_text = review_text
        self.platform = platform
        self.pros = pros
        self.cons = cons
        self.image_path = image_path
        self.likes_count = likes_count
        self.views_count = views_count
        self.created_at = created_at or datetime.now()
    
    def to_dict(self) -> Dict:
        """Convert review object to dictionary"""
        return {
            'id': self.id,
            'game_id': self.game_id,
            'reviewer_name': self.reviewer_name,
            'rating': self.rating,
            'review_text': self.review_text,
            'platform': self.platform,
            'pros': self.pros.split('|') if self.pros else [],
            'cons': self.cons.split('|') if self.cons else [],
            'image_path': self.image_path,
            'likes_count': self.likes_count,
            'views_count': self.views_count,
            'created_at': self.created_at.isoformat() if isinstance(self.created_at, datetime) else self.created_at
        }
    
    @staticmethod
    def from_row(row) -> 'Review':
        """Create Review object from database row"""
        return Review(
            id=row['id'],
            game_id=row['game_id'],
            reviewer_name=row['reviewer_name'],
            rating=row['rating'],
            review_text=row['review_text'],
            platform=row['platform'],
            pros=row['pros'],
            cons=row['cons'],
            image_path=row['image_path'],
            likes_count=row['likes_count'],
            views_count=row['views_count'],
            created_at=row['created_at']
        )
    
    @staticmethod
    def get_all(page=1, limit=20, game_id=None):
        """Get all reviews with optional filtering and pagination"""
        offset = (page - 1) * limit
        
        # Build query with game information
        query = """
            SELECT r.*, g.title as game_title, g.cover_image as game_cover
            FROM reviews r
            JOIN games g ON r.game_id = g.id
            WHERE 1=1
        """
        params = []
        
        if game_id:
            query += " AND r.game_id = ?"
            params.append(game_id)
        
        query += " ORDER BY r.created_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        rows = execute_query(query, tuple(params))
        
        reviews = []
        for row in rows:
            review = Review.from_row(row).to_dict()
            review['game_title'] = row['game_title']
            review['game_cover'] = row['game_cover']
            reviews.append(review)
        
        return reviews
    
    @staticmethod
    def get_by_id(review_id: int) -> Optional[Dict]:
        """Get review by ID and increment view count"""
        # Increment view count
        execute_update(
            "UPDATE reviews SET views_count = views_count + 1 WHERE id = ?",
            (review_id,)
        )
        
        # Get review with game info
        rows = execute_query(
            """SELECT r.*, g.title as game_title, g.cover_image as game_cover
               FROM reviews r
               JOIN games g ON r.game_id = g.id
               WHERE r.id = ?""",
            (review_id,)
        )
        
        if not rows:
            return None
        
        row = rows[0]
        review = Review.from_row(row).to_dict()
        review['game_title'] = row['game_title']
        review['game_cover'] = row['game_cover']
        
        return review
    
    @staticmethod
    def get_for_game(game_id: int, page=1, limit=20, sort_by='newest'):
        """Get all reviews for a specific game"""
        offset = (page - 1) * limit
        
        # Build query with sorting
        query = "SELECT * FROM reviews WHERE game_id = ?"
        params = [game_id]
        
        if sort_by == 'oldest':
            query += " ORDER BY created_at ASC"
        elif sort_by == 'rating_high':
            query += " ORDER BY rating DESC, created_at DESC"
        elif sort_by == 'rating_low':
            query += " ORDER BY rating ASC, created_at DESC"
        elif sort_by == 'likes':
            query += " ORDER BY likes_count DESC, created_at DESC"
        else:  # newest (default)
            query += " ORDER BY created_at DESC"
        
        query += " LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        rows = execute_query(query, tuple(params))
        return [Review.from_row(row).to_dict() for row in rows]
    
    def save(self) -> int:
        """Save review to database"""
        if self.id:
            # Update existing review
            execute_update(
                """UPDATE reviews 
                   SET reviewer_name=?, rating=?, review_text=?, platform=?, 
                       pros=?, cons=?, image_path=?
                   WHERE id=?""",
                (self.reviewer_name, self.rating, self.review_text, self.platform,
                 self.pros, self.cons, self.image_path, self.id)
            )
            return self.id
        else:
            # Insert new review
            review_id = execute_insert(
                """INSERT INTO reviews 
                   (game_id, reviewer_name, rating, review_text, platform, pros, cons, image_path)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                (self.game_id, self.reviewer_name, self.rating, self.review_text,
                 self.platform, self.pros, self.cons, self.image_path)
            )
            self.id = review_id
            
            # Update game rating
            update_game_rating(self.game_id)
            
            return review_id
    
    def like(self) -> int:
        """Like a review (increment like count)"""
        execute_update(
            "UPDATE reviews SET likes_count = likes_count + 1 WHERE id = ?",
            (self.id,)
        )
        
        # Get updated like count
        rows = execute_query(
            "SELECT likes_count FROM reviews WHERE id = ?", (self.id,)
        )
        return rows[0]['likes_count'] if rows else 0

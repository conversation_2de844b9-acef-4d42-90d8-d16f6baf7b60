#!/usr/bin/env python3
"""
GameReview Professional Flask Application - Legacy Entry Point
This file maintains backward compatibility while using the new organized structure
"""

import os
import sys

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.config import DevelopmentConfig

def main():
    """Main application entry point with legacy compatibility"""

    # Create app with development configuration
    app = create_app(DevelopmentConfig)

    # Load sample data for development
    try:
        from data.sample_data import populate_sample_data
        populate_sample_data()
        print("✅ Sample data loaded successfully!")
    except ImportError:
        print("⚠️  sample_data.py not found - skipping sample data population")
    except Exception as e:
        print(f"⚠️  Error loading sample data: {e}")

    # Print startup information
    print("\n" + "="*60)
    print("🎮 GameReview Pro - Professional Flask Application")
    print("="*60)
    print("📊 Environment: Development (Legacy Entry Point)")
    print("🌐 API Base URL: http://localhost:5000/api")
    print("📁 Static Files: http://localhost:5000/static")
    print("="*60)
    print("🚀 Starting server...")
    print("="*60 + "\n")

    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)

if __name__ == '__main__':
    main()

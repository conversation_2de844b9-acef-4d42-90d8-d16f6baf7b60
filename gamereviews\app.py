#!/usr/bin/env python3
"""
GameReview Mobile App - Flask Backend
A simplified mobile app for game reviews without authentication
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import sqlite3
import os
from datetime import datetime
import uuid
from werkzeug.utils import secure_filename
from PIL import Image
import json

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for React Native

# Configuration
app.config['SECRET_KEY'] = 'gamereviews-mobile-app-2024'
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['STATIC_FOLDER'] = 'static'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Allowed file extensions for image uploads
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

# Create necessary directories
os.makedirs('static/uploads', exist_ok=True)
os.makedirs('static/images', exist_ok=True)

def allowed_file(filename):
    """Check if uploaded file has allowed extension"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_db_connection():
    """Get database connection with row factory"""
    conn = sqlite3.connect('database.db')
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """Initialize database with tables"""
    conn = get_db_connection()
    
    # Create games table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS games (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            genre TEXT,
            platforms TEXT,
            cover_image TEXT,
            average_rating REAL DEFAULT 0.0,
            total_reviews INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create reviews table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS reviews (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            game_id INTEGER,
            reviewer_name TEXT DEFAULT 'Anonymous',
            rating INTEGER NOT NULL,
            review_text TEXT,
            platform TEXT,
            pros TEXT,
            cons TEXT,
            image_path TEXT,
            likes_count INTEGER DEFAULT 0,
            views_count INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (game_id) REFERENCES games(id)
        )
    ''')
    
    conn.commit()
    conn.close()
    print("Database initialized successfully!")

def update_game_rating(game_id):
    """Update game's average rating and total reviews count"""
    conn = get_db_connection()
    
    # Calculate new average rating
    result = conn.execute('''
        SELECT AVG(rating) as avg_rating, COUNT(*) as total_reviews
        FROM reviews 
        WHERE game_id = ?
    ''', (game_id,)).fetchone()
    
    avg_rating = result['avg_rating'] if result['avg_rating'] else 0.0
    total_reviews = result['total_reviews']
    
    # Update game record
    conn.execute('''
        UPDATE games 
        SET average_rating = ?, total_reviews = ?
        WHERE id = ?
    ''', (round(avg_rating, 1), total_reviews, game_id))
    
    conn.commit()
    conn.close()

# Import routes
from routes import *

if __name__ == '__main__':
    # Initialize database on startup
    init_database()
    
    # Import and run sample data population
    try:
        from sample_data import populate_sample_data
        populate_sample_data()
        print("Sample data loaded successfully!")
    except ImportError:
        print("sample_data.py not found - skipping sample data population")
    except Exception as e:
        print(f"Error loading sample data: {e}")
    
    # Run the Flask app
    print("Starting GameReview Mobile App Backend...")
    print("API will be available at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)

# GameReview Pro - Professional Flask Backend

A professionally organized, production-ready Flask REST API for the GameReview mobile application.

## 🏗️ Architecture Overview

This backend follows professional Flask application patterns with:
- **Modular Structure** - Organized into logical modules and packages
- **Blueprint Architecture** - Separated API routes for maintainability
- **Configuration Management** - Environment-based configuration
- **Error Handling** - Comprehensive error handling and logging
- **Data Validation** - Input validation and sanitization
- **File Management** - Professional file upload and processing

## 📁 Project Structure

```
gamereviews/
├── app/                    # Main application package
│   ├── __init__.py        # Application factory
│   ├── config.py          # Configuration management
│   ├── database.py        # Database operations
│   ├── static_files.py    # Static file serving
│   ├── api/               # API blueprints
│   │   ├── __init__.py    # API blueprint registration
│   │   ├── games.py       # Games API routes
│   │   ├── reviews.py     # Reviews API routes
│   │   ├── uploads.py     # File upload routes
│   │   └── utils.py       # Utility API routes
│   ├── models/            # Data models
│   │   ├── __init__.py    # Models package
│   │   ├── game.py        # Game model
│   │   └── review.py      # Review model
│   └── utils/             # Utility modules
│       ├── __init__.py    # Utils package
│       ├── file_handler.py # File operations
│       ├── validators.py   # Data validation
│       ├── helpers.py     # Helper functions
│       └── error_handlers.py # Error handling
├── data/                  # Data management
│   ├── __init__.py       # Data package
│   └── sample_data.py    # Sample data population
├── static/               # Static files
│   ├── images/          # Game cover images
│   └── uploads/         # User uploaded files
├── app.py               # Legacy entry point
├── run.py               # Modern entry point
├── requirements.txt     # Dependencies
├── database.db          # SQLite database (auto-created)
└── README.md           # This documentation
```

## 🚀 Quick Start

### Method 1: Modern Entry Point (Recommended)
```bash
# Install dependencies
pip install -r requirements.txt

# Run with modern structure
python run.py
```

### Method 2: Legacy Entry Point
```bash
# Install dependencies
pip install -r requirements.txt

# Run with legacy compatibility
python app.py
```

## 🔧 Configuration

The application supports multiple environments:

- **Development** (default): Debug enabled, SQLite database
- **Production**: Optimized for deployment, PostgreSQL support
- **Testing**: In-memory database, testing optimizations

Set environment with:
```bash
export FLASK_ENV=production  # or development, testing
```

## 📊 API Documentation

### Base URL
- Development: `http://localhost:5000/api`
- Health Check: `http://localhost:5000/api/health`

### Games API
- `GET /api/games` - List games with filtering and pagination
- `GET /api/games/{id}` - Get specific game details
- `GET /api/games/search?q={query}` - Search games by title
- `GET /api/games/top-rated` - Get highest rated games
- `GET /api/games/{id}/stats` - Get detailed game statistics

### Reviews API
- `GET /api/reviews` - List latest reviews
- `GET /api/reviews/{id}` - Get specific review (increments view count)
- `POST /api/reviews` - Create new review
- `PUT /api/reviews/{id}/like` - Like a review
- `GET /api/reviews/game/{game_id}` - Get reviews for specific game
- `PUT /api/reviews/{id}/image` - Add image to existing review

### Upload API
- `POST /api/upload/review-image` - Upload review image
- `POST /api/upload/game-cover` - Upload game cover (admin)

### Utility API
- `GET /api/platforms` - Get available gaming platforms
- `GET /api/genres` - Get available game genres
- `GET /api/stats` - Get application statistics
- `GET /api/search/suggestions` - Get search suggestions
- `GET /api/trending` - Get trending games and reviews

## 🗄️ Database

### Technology
- **Development**: SQLite (file-based)
- **Production**: PostgreSQL (recommended)
- **Testing**: In-memory SQLite

### Features
- **Auto-initialization** - Tables created automatically
- **Indexing** - Optimized queries with proper indexes
- **Relationships** - Foreign key constraints
- **Sample Data** - 50+ games and reviews for testing

### Models
- **Game**: Title, description, genre, platforms, ratings
- **Review**: Rating, text, pros/cons, images, social metrics

## 📱 Mobile Integration

Optimized for React Native mobile apps:
- **CORS Enabled** - Cross-origin requests supported
- **Image Processing** - Automatic image optimization
- **Fast Responses** - Efficient database queries
- **Error Handling** - Mobile-friendly error messages
- **File Upload** - Secure image upload with validation

## 🔒 Security Features

- **Input Validation** - Comprehensive data validation
- **File Security** - Safe file upload with type checking
- **Error Handling** - Secure error messages
- **SQL Injection Protection** - Parameterized queries
- **File Size Limits** - Configurable upload limits

## 🧪 Testing

### Sample Data
- **50+ Popular Games** - Across all genres and platforms
- **20+ Sample Reviews** - With ratings, pros/cons, and metadata
- **No Authentication** - Immediate testing without barriers

### API Testing
```bash
# Test health endpoint
curl http://localhost:5000/api/health

# Test games endpoint
curl "http://localhost:5000/api/games?limit=5"

# Test reviews endpoint
curl "http://localhost:5000/api/reviews?limit=3"
```

## 🚀 Production Deployment

### Requirements
1. **WSGI Server**: gunicorn, uWSGI, or similar
2. **Database**: PostgreSQL or MySQL
3. **File Storage**: AWS S3, Google Cloud Storage
4. **Environment Variables**: Secure configuration
5. **Monitoring**: Application and error monitoring

### Environment Variables
```bash
FLASK_ENV=production
SECRET_KEY=your-secret-key
DATABASE_URL=postgresql://user:pass@host:port/db
UPLOAD_FOLDER=/path/to/uploads
```

### Production Checklist
- [ ] Set `FLASK_ENV=production`
- [ ] Configure production database
- [ ] Set up file storage service
- [ ] Configure logging and monitoring
- [ ] Set secure secret key
- [ ] Enable HTTPS
- [ ] Configure rate limiting
- [ ] Set up backup strategy

## 🔧 Development

### Code Quality
- **Type Hints** - Python type annotations
- **Documentation** - Comprehensive docstrings
- **Error Handling** - Graceful error management
- **Validation** - Input validation and sanitization
- **Logging** - Structured logging for debugging

### Adding New Features
1. **Models**: Add to `app/models/`
2. **API Routes**: Add to `app/api/`
3. **Utilities**: Add to `app/utils/`
4. **Tests**: Add test cases
5. **Documentation**: Update API docs

## 📈 Performance

### Optimizations
- **Database Indexing** - Optimized query performance
- **Image Processing** - Automatic image optimization
- **Caching Ready** - Structure supports caching layers
- **Pagination** - Efficient data pagination
- **Connection Pooling** - Database connection management

### Monitoring
- **Health Checks** - Built-in health monitoring
- **Error Tracking** - Comprehensive error logging
- **Performance Metrics** - Response time tracking
- **Usage Statistics** - API usage analytics

## 🤝 Contributing

1. Follow the established project structure
2. Add proper error handling and validation
3. Include comprehensive documentation
4. Write tests for new features
5. Follow Python best practices

## 📞 Support

For technical support and questions:
- Review the API documentation
- Check error logs for debugging
- Refer to the project structure guide
- Follow the development guidelines

---

**GameReview Pro Backend** - Professional Flask API for Modern Mobile Applications

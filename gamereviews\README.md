# GameReview Mobile App - Backend

A Flask-based REST API for the GameReview mobile application.

## Features

- **No Authentication Required** - Easy testing without login/signup
- **Game Database** - 50+ pre-loaded popular games
- **Review System** - Star ratings, text reviews, pros/cons, images
- **Image Upload** - Optimized image handling for mobile
- **Search & Filter** - Find games and reviews easily
- **Social Features** - Like reviews, view counts
- **Mobile Optimized** - Fast API responses for mobile apps

## Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Application**
   ```bash
   python app.py
   ```

3. **Access the API**
   - API Base URL: `http://localhost:5000`
   - Health Check: `http://localhost:5000/api/health`

## API Endpoints

### Games
- `GET /api/games` - Get all games (with pagination)
- `GET /api/games/{id}` - Get specific game
- `GET /api/games/search?q={query}` - Search games
- `GET /api/games/top-rated` - Get top rated games

### Reviews
- `GET /api/reviews` - Get latest reviews
- `GET /api/reviews/{id}` - Get specific review
- `POST /api/reviews` - Create new review
- `PUT /api/reviews/{id}/like` - Like a review
- `GET /api/reviews/game/{game_id}` - Get reviews for game

### Image Upload
- `POST /api/upload/review-image` - Upload review image
- `PUT /api/reviews/{id}/image` - Add image to review

### Utility
- `GET /api/platforms` - Get gaming platforms
- `GET /api/genres` - Get game genres
- `GET /api/stats` - Get app statistics

## Database

- **SQLite** - Simple file-based database
- **Auto-created** - Database and tables created on first run
- **Sample Data** - 50+ games and sample reviews pre-loaded

## File Structure

```
gamereviews/
├── app.py              # Main Flask application
├── models.py           # Database models
├── routes.py           # API routes
├── sample_data.py      # Sample data population
├── requirements.txt    # Python dependencies
├── database.db         # SQLite database (auto-created)
├── static/
│   ├── images/         # Game cover images
│   └── uploads/        # User uploaded images
└── README.md          # This file
```

## Testing

The app comes pre-loaded with sample data for immediate testing:
- 50+ popular games across all genres
- Sample reviews with ratings and comments
- No authentication barriers

## Mobile App Integration

This backend is designed to work with the React Native mobile app. The API provides:
- CORS enabled for mobile requests
- Optimized image handling
- Fast response times
- Simple JSON responses

## Development

- **Debug Mode**: Enabled by default
- **Hot Reload**: Flask auto-reloads on code changes
- **Error Handling**: Comprehensive error responses
- **Logging**: Console logging for debugging

## Production Notes

For production deployment:
1. Set `debug=False` in app.py
2. Use a production WSGI server (gunicorn, uWSGI)
3. Configure proper database (PostgreSQL, MySQL)
4. Set up proper file storage (AWS S3, etc.)
5. Add authentication and authorization
6. Implement rate limiting
7. Add comprehensive logging

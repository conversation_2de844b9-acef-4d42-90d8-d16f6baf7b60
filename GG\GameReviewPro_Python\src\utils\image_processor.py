"""
Image Processing utilities for GameReview Pro
Using OpenCV and PIL for advanced image operations
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter, ImageDraw, ImageFont
import os
import requests
from io import BytesIO
import base64
from typing import Tuple, Optional, List
import logging

class ImageProcessor:
    """Advanced image processing for game covers and review images"""
    
    def __init__(self):
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.webp', '.bmp']
        self.max_size = (1200, 1600)  # Max dimensions
        self.thumbnail_size = (300, 400)  # Thumbnail dimensions
        
        print("✅ Image Processor initialized")
    
    def load_image(self, image_path: str) -> Optional[np.ndarray]:
        """Load image from file path or URL"""
        try:
            if image_path.startswith(('http://', 'https://')):
                # Download image from URL
                response = requests.get(image_path, timeout=10)
                image = Image.open(BytesIO(response.content))
                return cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            else:
                # Load from local file
                return cv2.imread(image_path)
        except Exception as e:
            logging.error(f"Error loading image {image_path}: {e}")
            return None
    
    def save_image(self, image: np.ndarray, output_path: str, quality: int = 85) -> bool:
        """Save image with optimization"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Convert BGR to RGB for PIL
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)
            
            # Save with optimization
            if output_path.lower().endswith('.jpg') or output_path.lower().endswith('.jpeg'):
                pil_image.save(output_path, 'JPEG', quality=quality, optimize=True)
            else:
                pil_image.save(output_path, optimize=True)
            
            return True
        except Exception as e:
            logging.error(f"Error saving image {output_path}: {e}")
            return False
    
    def resize_image(self, image: np.ndarray, target_size: Tuple[int, int], 
                    maintain_aspect: bool = True) -> np.ndarray:
        """Resize image with optional aspect ratio preservation"""
        try:
            height, width = image.shape[:2]
            target_width, target_height = target_size
            
            if maintain_aspect:
                # Calculate scaling factor
                scale = min(target_width / width, target_height / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                
                # Resize image
                resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
                
                # Create canvas and center image
                canvas = np.zeros((target_height, target_width, 3), dtype=np.uint8)
                y_offset = (target_height - new_height) // 2
                x_offset = (target_width - new_width) // 2
                canvas[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized
                
                return canvas
            else:
                return cv2.resize(image, target_size, interpolation=cv2.INTER_LANCZOS4)
                
        except Exception as e:
            logging.error(f"Error resizing image: {e}")
            return image
    
    def create_thumbnail(self, image: np.ndarray, size: Tuple[int, int] = None) -> np.ndarray:
        """Create thumbnail with smart cropping"""
        if size is None:
            size = self.thumbnail_size
        
        try:
            # Use smart cropping to focus on center
            height, width = image.shape[:2]
            target_width, target_height = size
            
            # Calculate crop area (center crop)
            aspect_ratio = target_width / target_height
            image_aspect = width / height
            
            if image_aspect > aspect_ratio:
                # Image is wider, crop width
                new_width = int(height * aspect_ratio)
                x_offset = (width - new_width) // 2
                cropped = image[:, x_offset:x_offset+new_width]
            else:
                # Image is taller, crop height
                new_height = int(width / aspect_ratio)
                y_offset = (height - new_height) // 2
                cropped = image[y_offset:y_offset+new_height, :]
            
            # Resize to target size
            thumbnail = cv2.resize(cropped, size, interpolation=cv2.INTER_LANCZOS4)
            
            return thumbnail
            
        except Exception as e:
            logging.error(f"Error creating thumbnail: {e}")
            return self.resize_image(image, size)
    
    def enhance_image(self, image: np.ndarray, brightness: float = 1.0, 
                     contrast: float = 1.0, saturation: float = 1.0) -> np.ndarray:
        """Enhance image with brightness, contrast, and saturation adjustments"""
        try:
            # Convert to PIL for enhancement
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)
            
            # Apply enhancements
            if brightness != 1.0:
                enhancer = ImageEnhance.Brightness(pil_image)
                pil_image = enhancer.enhance(brightness)
            
            if contrast != 1.0:
                enhancer = ImageEnhance.Contrast(pil_image)
                pil_image = enhancer.enhance(contrast)
            
            if saturation != 1.0:
                enhancer = ImageEnhance.Color(pil_image)
                pil_image = enhancer.enhance(saturation)
            
            # Convert back to OpenCV format
            enhanced = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            return enhanced
            
        except Exception as e:
            logging.error(f"Error enhancing image: {e}")
            return image
    
    def add_game_rating_overlay(self, image: np.ndarray, rating: float, 
                               total_reviews: int = 0) -> np.ndarray:
        """Add rating overlay to game cover image"""
        try:
            # Convert to PIL for text rendering
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)
            draw = ImageDraw.Draw(pil_image)
            
            # Create rating badge
            width, height = pil_image.size
            badge_size = min(width, height) // 4
            badge_x = width - badge_size - 10
            badge_y = 10
            
            # Draw badge background
            draw.ellipse(
                [badge_x, badge_y, badge_x + badge_size, badge_y + badge_size],
                fill=(0, 0, 0, 180),
                outline=(255, 255, 255)
            )
            
            # Add rating text
            try:
                font = ImageFont.truetype("arial.ttf", badge_size // 3)
            except:
                font = ImageFont.load_default()
            
            rating_text = f"{rating:.1f}"
            text_bbox = draw.textbbox((0, 0), rating_text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            text_x = badge_x + (badge_size - text_width) // 2
            text_y = badge_y + (badge_size - text_height) // 2
            
            draw.text((text_x, text_y), rating_text, fill=(255, 255, 255), font=font)
            
            # Add star
            star_y = text_y + text_height + 5
            star_text = "★"
            star_bbox = draw.textbbox((0, 0), star_text, font=font)
            star_width = star_bbox[2] - star_bbox[0]
            star_x = badge_x + (badge_size - star_width) // 2
            
            draw.text((star_x, star_y), star_text, fill=(255, 215, 0), font=font)
            
            # Convert back to OpenCV
            result = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            return result
            
        except Exception as e:
            logging.error(f"Error adding rating overlay: {e}")
            return image
    
    def create_game_collage(self, images: List[np.ndarray], grid_size: Tuple[int, int] = (2, 2)) -> np.ndarray:
        """Create a collage of game images"""
        try:
            rows, cols = grid_size
            if len(images) < rows * cols:
                # Pad with black images if needed
                black_image = np.zeros((300, 200, 3), dtype=np.uint8)
                images.extend([black_image] * (rows * cols - len(images)))
            
            # Resize all images to same size
            cell_size = (200, 300)
            resized_images = [self.resize_image(img, cell_size, maintain_aspect=True) for img in images[:rows * cols]]
            
            # Create collage
            collage_height = cell_size[1] * rows
            collage_width = cell_size[0] * cols
            collage = np.zeros((collage_height, collage_width, 3), dtype=np.uint8)
            
            for i, img in enumerate(resized_images):
                row = i // cols
                col = i % cols
                y_start = row * cell_size[1]
                y_end = y_start + cell_size[1]
                x_start = col * cell_size[0]
                x_end = x_start + cell_size[0]
                
                collage[y_start:y_end, x_start:x_end] = img
            
            return collage
            
        except Exception as e:
            logging.error(f"Error creating collage: {e}")
            return np.zeros((600, 400, 3), dtype=np.uint8)
    
    def detect_game_logo(self, image: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """Detect game logo/title area in cover image using computer vision"""
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply edge detection
            edges = cv2.Canny(gray, 50, 150)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Find largest rectangular contour (likely logo area)
            largest_area = 0
            best_rect = None
            
            for contour in contours:
                # Approximate contour to polygon
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # Check if it's roughly rectangular
                if len(approx) >= 4:
                    area = cv2.contourArea(contour)
                    if area > largest_area:
                        largest_area = area
                        x, y, w, h = cv2.boundingRect(contour)
                        best_rect = (x, y, w, h)
            
            return best_rect
            
        except Exception as e:
            logging.error(f"Error detecting game logo: {e}")
            return None
    
    def apply_gaming_filter(self, image: np.ndarray, filter_type: str = "neon") -> np.ndarray:
        """Apply gaming-themed filters to images"""
        try:
            if filter_type == "neon":
                # Create neon effect
                # Increase contrast and saturation
                enhanced = self.enhance_image(image, brightness=1.1, contrast=1.3, saturation=1.4)
                
                # Add slight blur and glow effect
                blurred = cv2.GaussianBlur(enhanced, (3, 3), 0)
                result = cv2.addWeighted(enhanced, 0.7, blurred, 0.3, 0)
                
                return result
                
            elif filter_type == "retro":
                # Create retro gaming effect
                # Reduce colors (posterize effect)
                result = image.copy()
                result = result // 32 * 32  # Reduce color depth
                
                # Add slight sepia tone
                kernel = np.array([[0.272, 0.534, 0.131],
                                 [0.349, 0.686, 0.168],
                                 [0.393, 0.769, 0.189]])
                result = cv2.transform(result, kernel)
                
                return np.clip(result, 0, 255).astype(np.uint8)
                
            elif filter_type == "cyberpunk":
                # Create cyberpunk effect
                # Split into color channels
                b, g, r = cv2.split(image)
                
                # Enhance blue and reduce red
                b = cv2.add(b, 30)
                r = cv2.subtract(r, 20)
                
                # Merge channels
                result = cv2.merge([b, g, r])
                
                # Add contrast
                result = self.enhance_image(result, contrast=1.2)
                
                return result
            
            else:
                return image
                
        except Exception as e:
            logging.error(f"Error applying gaming filter: {e}")
            return image
    
    def extract_dominant_colors(self, image: np.ndarray, k: int = 5) -> List[Tuple[int, int, int]]:
        """Extract dominant colors from image using K-means clustering"""
        try:
            # Reshape image to be a list of pixels
            data = image.reshape((-1, 3))
            data = np.float32(data)
            
            # Apply K-means clustering
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
            _, labels, centers = cv2.kmeans(data, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
            
            # Convert centers to integers and return as list of tuples
            centers = np.uint8(centers)
            dominant_colors = [tuple(color) for color in centers]
            
            return dominant_colors
            
        except Exception as e:
            logging.error(f"Error extracting dominant colors: {e}")
            return [(128, 128, 128)]  # Return gray as fallback
    
    def create_placeholder_image(self, size: Tuple[int, int], text: str = "No Image") -> np.ndarray:
        """Create placeholder image with text"""
        try:
            width, height = size
            
            # Create PIL image
            pil_image = Image.new('RGB', (width, height), color=(64, 64, 64))
            draw = ImageDraw.Draw(pil_image)
            
            # Add text
            try:
                font_size = min(width, height) // 8
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                font = ImageFont.load_default()
            
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            text_x = (width - text_width) // 2
            text_y = (height - text_height) // 2
            
            draw.text((text_x, text_y), text, fill=(200, 200, 200), font=font)
            
            # Convert to OpenCV format
            opencv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            return opencv_image
            
        except Exception as e:
            logging.error(f"Error creating placeholder image: {e}")
            return np.zeros((height, width, 3), dtype=np.uint8)

"""
AI Sentiment Analysis for GameReview Pro
Using PyTorch and Transformers for advanced text analysis
"""

import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline
import numpy as np
from textblob import TextBlob
import nltk
from nltk.sentiment import SentimentIntensityAnalyzer
import json
import logging

# Download required NLTK data
try:
    nltk.download('vader_lexicon', quiet=True)
    nltk.download('punkt', quiet=True)
except:
    pass

class SentimentAnalyzer:
    """Advanced sentiment analysis using multiple models"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔥 Using device: {self.device}")
        
        # Initialize models
        self.init_models()
        
        # NLTK VADER for baseline
        self.vader = SentimentIntensityAnalyzer()
        
        print("✅ Sentiment Analyzer initialized successfully!")
    
    def init_models(self):
        """Initialize AI models"""
        try:
            # Gaming-specific sentiment model (using general model as fallback)
            model_name = "cardiffnlp/twitter-roberta-base-sentiment-latest"
            
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForSequenceClassification.from_pretrained(model_name)
            self.model.to(self.device)
            
            # Emotion analysis pipeline
            self.emotion_pipeline = pipeline(
                "text-classification",
                model="j-hartmann/emotion-english-distilroberta-base",
                device=0 if torch.cuda.is_available() else -1
            )
            
            print("✅ AI models loaded successfully!")
            
        except Exception as e:
            print(f"⚠️ Error loading AI models: {e}")
            print("📝 Using fallback models...")
            self.model = None
            self.tokenizer = None
            self.emotion_pipeline = None
    
    def analyze_sentiment(self, text):
        """
        Comprehensive sentiment analysis
        Returns: dict with sentiment scores and emotions
        """
        if not text or not text.strip():
            return self.get_neutral_result()
        
        results = {
            'sentiment_score': 0.0,
            'confidence': 0.0,
            'emotions': {},
            'vader_scores': {},
            'textblob_polarity': 0.0,
            'gaming_context': {}
        }
        
        try:
            # 1. VADER Sentiment (good for social media text)
            vader_scores = self.vader.polarity_scores(text)
            results['vader_scores'] = vader_scores
            
            # 2. TextBlob sentiment
            blob = TextBlob(text)
            results['textblob_polarity'] = blob.sentiment.polarity
            
            # 3. Transformer-based sentiment
            if self.model and self.tokenizer:
                transformer_sentiment = self.get_transformer_sentiment(text)
                results.update(transformer_sentiment)
            
            # 4. Emotion analysis
            if self.emotion_pipeline:
                emotions = self.get_emotions(text)
                results['emotions'] = emotions
            
            # 5. Gaming-specific context analysis
            gaming_context = self.analyze_gaming_context(text)
            results['gaming_context'] = gaming_context
            
            # 6. Calculate final sentiment score (-1 to 1)
            final_score = self.calculate_final_sentiment(results)
            results['sentiment_score'] = final_score
            
        except Exception as e:
            logging.error(f"Error in sentiment analysis: {e}")
            return self.get_neutral_result()
        
        return results
    
    def get_transformer_sentiment(self, text):
        """Get sentiment using transformer model"""
        try:
            # Tokenize and predict
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, 
                                  padding=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = self.model(**inputs)
                predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
            
            # Convert to sentiment score
            scores = predictions.cpu().numpy()[0]
            
            # Assuming model outputs [negative, neutral, positive]
            if len(scores) == 3:
                sentiment_score = scores[2] - scores[0]  # positive - negative
                confidence = max(scores)
            else:
                sentiment_score = 0.0
                confidence = 0.5
            
            return {
                'transformer_sentiment': sentiment_score,
                'confidence': float(confidence)
            }
            
        except Exception as e:
            logging.error(f"Transformer sentiment error: {e}")
            return {'transformer_sentiment': 0.0, 'confidence': 0.0}
    
    def get_emotions(self, text):
        """Analyze emotions in text"""
        try:
            if not self.emotion_pipeline:
                return {}
            
            # Get emotion predictions
            emotions = self.emotion_pipeline(text)
            
            # Convert to dictionary
            emotion_dict = {}
            for emotion in emotions:
                emotion_dict[emotion['label']] = emotion['score']
            
            return emotion_dict
            
        except Exception as e:
            logging.error(f"Emotion analysis error: {e}")
            return {}
    
    def analyze_gaming_context(self, text):
        """Analyze gaming-specific sentiment indicators"""
        text_lower = text.lower()
        
        # Gaming-specific positive indicators
        positive_gaming = [
            'amazing', 'incredible', 'masterpiece', 'perfect', 'outstanding',
            'addictive', 'immersive', 'engaging', 'polished', 'smooth',
            'beautiful graphics', 'great story', 'excellent gameplay',
            'worth buying', 'highly recommend', 'game of the year'
        ]
        
        # Gaming-specific negative indicators
        negative_gaming = [
            'buggy', 'glitchy', 'broken', 'unfinished', 'disappointing',
            'repetitive', 'boring', 'waste of money', 'not worth it',
            'terrible graphics', 'bad story', 'poor gameplay',
            'cash grab', 'overhyped', 'unplayable'
        ]
        
        # Count indicators
        positive_count = sum(1 for word in positive_gaming if word in text_lower)
        negative_count = sum(1 for word in negative_gaming if word in text_lower)
        
        # Calculate gaming context score
        total_indicators = positive_count + negative_count
        if total_indicators > 0:
            gaming_sentiment = (positive_count - negative_count) / total_indicators
        else:
            gaming_sentiment = 0.0
        
        return {
            'gaming_sentiment': gaming_sentiment,
            'positive_indicators': positive_count,
            'negative_indicators': negative_count,
            'gaming_specific_words': total_indicators
        }
    
    def calculate_final_sentiment(self, results):
        """Calculate weighted final sentiment score"""
        scores = []
        weights = []
        
        # VADER compound score
        if 'vader_scores' in results and 'compound' in results['vader_scores']:
            scores.append(results['vader_scores']['compound'])
            weights.append(0.3)
        
        # TextBlob polarity
        if 'textblob_polarity' in results:
            scores.append(results['textblob_polarity'])
            weights.append(0.2)
        
        # Transformer sentiment
        if 'transformer_sentiment' in results:
            scores.append(results['transformer_sentiment'])
            weights.append(0.3)
        
        # Gaming context
        if 'gaming_context' in results and 'gaming_sentiment' in results['gaming_context']:
            scores.append(results['gaming_context']['gaming_sentiment'])
            weights.append(0.2)
        
        # Calculate weighted average
        if scores and weights:
            final_score = np.average(scores, weights=weights)
            return float(np.clip(final_score, -1.0, 1.0))
        
        return 0.0
    
    def get_neutral_result(self):
        """Return neutral sentiment result"""
        return {
            'sentiment_score': 0.0,
            'confidence': 0.0,
            'emotions': {},
            'vader_scores': {'compound': 0.0, 'pos': 0.0, 'neu': 1.0, 'neg': 0.0},
            'textblob_polarity': 0.0,
            'gaming_context': {'gaming_sentiment': 0.0}
        }
    
    def analyze_review_quality(self, text):
        """Analyze review quality and helpfulness"""
        if not text or not text.strip():
            return {'quality_score': 0.0, 'helpfulness_score': 0.0}
        
        # Length analysis
        word_count = len(text.split())
        length_score = min(word_count / 100, 1.0)  # Normalize to 0-1
        
        # Readability (simple version)
        sentences = text.split('.')
        avg_sentence_length = word_count / max(len(sentences), 1)
        readability_score = max(0, 1 - (avg_sentence_length - 15) / 20)
        
        # Specific details (gaming terms)
        gaming_terms = [
            'gameplay', 'graphics', 'story', 'controls', 'soundtrack',
            'multiplayer', 'campaign', 'difficulty', 'performance',
            'bugs', 'features', 'mechanics', 'design', 'art style'
        ]
        
        detail_score = sum(1 for term in gaming_terms if term in text.lower())
        detail_score = min(detail_score / 5, 1.0)  # Normalize
        
        # Calculate final scores
        quality_score = (length_score * 0.3 + readability_score * 0.3 + detail_score * 0.4)
        helpfulness_score = (detail_score * 0.6 + length_score * 0.4)
        
        return {
            'quality_score': float(quality_score),
            'helpfulness_score': float(helpfulness_score),
            'readability_score': float(readability_score),
            'word_count': word_count,
            'gaming_terms_count': int(detail_score * 5)
        }

# Global sentiment analyzer instance
sentiment_analyzer = SentimentAnalyzer()

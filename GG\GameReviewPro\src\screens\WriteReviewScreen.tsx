// Professional Write Review Screen
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Toast from 'react-native-toast-message';
import { Game, NavigationProps, CreateReviewRequest } from '../types';
import { colors, typography, layout, shadows } from '../theme';
import { apiService } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import GameCard from '../components/GameCard';

export const WriteReviewScreen: React.FC<NavigationProps> = ({ navigation, route }) => {
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [games, setGames] = useState<Game[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  // Review form data
  const [reviewerName, setReviewerName] = useState('');
  const [rating, setRating] = useState(0);
  const [reviewText, setReviewText] = useState('');
  const [platform, setPlatform] = useState('');
  const [pros, setPros] = useState(['']);
  const [cons, setCons] = useState(['']);
  
  const [platforms, setPlatforms] = useState<string[]>([]);

  useEffect(() => {
    loadPlatforms();
    if (route?.params?.gameId) {
      loadGame(route.params.gameId);
    }
  }, []);

  const loadPlatforms = async () => {
    try {
      const response = await apiService.getPlatforms();
      if (response.success && response.data) {
        setPlatforms(response.data);
      }
    } catch (error) {
      console.error('Error loading platforms:', error);
    }
  };

  const loadGame = async (gameId: number) => {
    try {
      setLoading(true);
      const response = await apiService.getGame(gameId);
      if (response.success && response.data) {
        setSelectedGame(response.data);
      }
    } catch (error) {
      console.error('Error loading game:', error);
    } finally {
      setLoading(false);
    }
  };

  const searchGames = async (query: string) => {
    if (!query.trim()) {
      setGames([]);
      return;
    }

    try {
      setLoading(true);
      const response = await apiService.searchGames(query);
      if (response.success && response.data) {
        setGames(response.data);
      }
    } catch (error) {
      console.error('Error searching games:', error);
    } finally {
      setLoading(false);
    }
  };

  const selectGame = (game: Game) => {
    setSelectedGame(game);
    setGames([]);
    setSearchQuery('');
  };

  const renderStars = () => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <TouchableOpacity
          key={i}
          onPress={() => setRating(i)}
          style={styles.starButton}
        >
          <Icon
            name={i <= rating ? 'star' : 'star-border'}
            size={32}
            color={i <= rating ? colors.gold : colors.lightGray}
          />
        </TouchableOpacity>
      );
    }
    return stars;
  };

  const addPro = () => {
    setPros([...pros, '']);
  };

  const addCon = () => {
    setCons([...cons, '']);
  };

  const updatePro = (index: number, value: string) => {
    const newPros = [...pros];
    newPros[index] = value;
    setPros(newPros);
  };

  const updateCon = (index: number, value: string) => {
    const newCons = [...cons];
    newCons[index] = value;
    setCons(newCons);
  };

  const removePro = (index: number) => {
    if (pros.length > 1) {
      setPros(pros.filter((_, i) => i !== index));
    }
  };

  const removeCon = (index: number) => {
    if (cons.length > 1) {
      setCons(cons.filter((_, i) => i !== index));
    }
  };

  const validateForm = (): boolean => {
    if (!selectedGame) {
      Alert.alert('Error', 'Please select a game to review');
      return false;
    }
    if (rating === 0) {
      Alert.alert('Error', 'Please provide a rating');
      return false;
    }
    return true;
  };

  const submitReview = async () => {
    if (!validateForm()) return;

    try {
      setSubmitting(true);

      const reviewData: CreateReviewRequest = {
        game_id: selectedGame!.id,
        reviewer_name: reviewerName.trim() || 'Anonymous',
        rating,
        review_text: reviewText.trim(),
        platform: platform,
        pros: pros.filter(p => p.trim()),
        cons: cons.filter(c => c.trim()),
      };

      const response = await apiService.createReview(reviewData);
      
      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'Review Submitted!',
          text2: 'Thank you for sharing your review',
        });
        
        // Reset form
        setSelectedGame(null);
        setReviewerName('');
        setRating(0);
        setReviewText('');
        setPlatform('');
        setPros(['']);
        setCons(['']);
        
        // Navigate back
        navigation.goBack();
      } else {
        Alert.alert('Error', response.error || 'Failed to submit review');
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      Alert.alert('Error', 'Failed to submit review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (submitting) {
    return <LoadingSpinner message="Submitting your review..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Write a Review</Text>
            <Text style={styles.subtitle}>Share your gaming experience</Text>
          </View>

          {/* Game Selection */}
          {!selectedGame ? (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Select Game</Text>
              <View style={styles.searchContainer}>
                <Icon name="search" size={20} color={colors.lightGray} />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search for a game..."
                  placeholderTextColor={colors.lightGray}
                  value={searchQuery}
                  onChangeText={(text) => {
                    setSearchQuery(text);
                    searchGames(text);
                  }}
                />
              </View>
              
              {loading && <LoadingSpinner size="small" />}
              
              {games.map((game) => (
                <TouchableOpacity
                  key={game.id}
                  onPress={() => selectGame(game)}
                  style={styles.gameOption}
                >
                  <GameCard game={game} onPress={selectGame} />
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View style={styles.section}>
              <View style={styles.selectedGameHeader}>
                <Text style={styles.sectionTitle}>Selected Game</Text>
                <TouchableOpacity
                  onPress={() => setSelectedGame(null)}
                  style={styles.changeButton}
                >
                  <Text style={styles.changeButtonText}>Change</Text>
                </TouchableOpacity>
              </View>
              <GameCard game={selectedGame} onPress={() => {}} />
            </View>
          )}

          {selectedGame && (
            <>
              {/* Reviewer Name */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Your Name (Optional)</Text>
                <TextInput
                  style={styles.textInput}
                  placeholder="Enter your name or leave blank for Anonymous"
                  placeholderTextColor={colors.lightGray}
                  value={reviewerName}
                  onChangeText={setReviewerName}
                />
              </View>

              {/* Rating */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Rating *</Text>
                <View style={styles.starsContainer}>
                  {renderStars()}
                </View>
                <Text style={styles.ratingText}>
                  {rating > 0 ? `${rating}/5 stars` : 'Tap to rate'}
                </Text>
              </View>

              {/* Platform */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Platform Played On</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View style={styles.platformContainer}>
                    {platforms.map((p) => (
                      <TouchableOpacity
                        key={p}
                        style={[
                          styles.platformChip,
                          platform === p && styles.platformChipSelected
                        ]}
                        onPress={() => setPlatform(p)}
                      >
                        <Text style={[
                          styles.platformText,
                          platform === p && styles.platformTextSelected
                        ]}>
                          {p}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>
              </View>

              {/* Review Text */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Your Review</Text>
                <TextInput
                  style={styles.textArea}
                  placeholder="Share your thoughts about this game..."
                  placeholderTextColor={colors.lightGray}
                  value={reviewText}
                  onChangeText={setReviewText}
                  multiline
                  numberOfLines={6}
                  textAlignVertical="top"
                />
              </View>

              {/* Pros */}
              <View style={styles.section}>
                <View style={styles.prosConsHeader}>
                  <Text style={styles.sectionTitle}>Pros</Text>
                  <TouchableOpacity onPress={addPro} style={styles.addButton}>
                    <Icon name="add" size={20} color={colors.green} />
                  </TouchableOpacity>
                </View>
                {pros.map((pro, index) => (
                  <View key={index} style={styles.prosConsItem}>
                    <TextInput
                      style={styles.prosConsInput}
                      placeholder="What did you like?"
                      placeholderTextColor={colors.lightGray}
                      value={pro}
                      onChangeText={(text) => updatePro(index, text)}
                    />
                    {pros.length > 1 && (
                      <TouchableOpacity
                        onPress={() => removePro(index)}
                        style={styles.removeButton}
                      >
                        <Icon name="remove" size={20} color={colors.red} />
                      </TouchableOpacity>
                    )}
                  </View>
                ))}
              </View>

              {/* Cons */}
              <View style={styles.section}>
                <View style={styles.prosConsHeader}>
                  <Text style={styles.sectionTitle}>Cons</Text>
                  <TouchableOpacity onPress={addCon} style={styles.addButton}>
                    <Icon name="add" size={20} color={colors.red} />
                  </TouchableOpacity>
                </View>
                {cons.map((con, index) => (
                  <View key={index} style={styles.prosConsItem}>
                    <TextInput
                      style={styles.prosConsInput}
                      placeholder="What could be improved?"
                      placeholderTextColor={colors.lightGray}
                      value={con}
                      onChangeText={(text) => updateCon(index, text)}
                    />
                    {cons.length > 1 && (
                      <TouchableOpacity
                        onPress={() => removeCon(index)}
                        style={styles.removeButton}
                      >
                        <Icon name="remove" size={20} color={colors.red} />
                      </TouchableOpacity>
                    )}
                  </View>
                ))}
              </View>

              {/* Submit Button */}
              <TouchableOpacity
                style={styles.submitButton}
                onPress={submitReview}
                disabled={submitting}
              >
                <Text style={styles.submitButtonText}>
                  {submitting ? 'Submitting...' : 'Submit Review'}
                </Text>
              </TouchableOpacity>
            </>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: layout.screenPadding,
    paddingBottom: layout.lg,
  },
  title: {
    ...typography.h1,
    color: colors.white,
    marginBottom: layout.xs,
  },
  subtitle: {
    ...typography.body,
    color: colors.lightGray,
  },
  section: {
    paddingHorizontal: layout.screenPadding,
    marginBottom: layout.lg,
  },
  sectionTitle: {
    ...typography.h3,
    color: colors.white,
    marginBottom: layout.sm,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    paddingHorizontal: layout.md,
    marginBottom: layout.md,
  },
  searchInput: {
    flex: 1,
    height: 48,
    marginLeft: layout.sm,
    ...typography.body,
    color: colors.white,
  },
  gameOption: {
    marginBottom: layout.sm,
  },
  selectedGameHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: layout.sm,
  },
  changeButton: {
    paddingHorizontal: layout.md,
    paddingVertical: layout.xs,
    backgroundColor: colors.primaryBlue,
    borderRadius: layout.borderRadius,
  },
  changeButtonText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '500',
  },
  textInput: {
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    paddingHorizontal: layout.md,
    paddingVertical: layout.sm,
    ...typography.body,
    color: colors.white,
    borderWidth: 1,
    borderColor: colors.mediumGray,
  },
  starsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: layout.sm,
  },
  starButton: {
    padding: layout.xs,
  },
  ratingText: {
    ...typography.body,
    color: colors.lightGray,
    textAlign: 'center',
  },
  platformContainer: {
    flexDirection: 'row',
    paddingRight: layout.screenPadding,
  },
  platformChip: {
    paddingHorizontal: layout.md,
    paddingVertical: layout.sm,
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    marginRight: layout.sm,
    borderWidth: 1,
    borderColor: colors.mediumGray,
  },
  platformChipSelected: {
    backgroundColor: colors.primaryBlue,
    borderColor: colors.primaryBlue,
  },
  platformText: {
    ...typography.caption,
    color: colors.lightGray,
  },
  platformTextSelected: {
    color: colors.white,
  },
  textArea: {
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    paddingHorizontal: layout.md,
    paddingVertical: layout.md,
    ...typography.body,
    color: colors.white,
    borderWidth: 1,
    borderColor: colors.mediumGray,
    minHeight: 120,
  },
  prosConsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: layout.sm,
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.darkGray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  prosConsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: layout.sm,
  },
  prosConsInput: {
    flex: 1,
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    paddingHorizontal: layout.md,
    paddingVertical: layout.sm,
    ...typography.body,
    color: colors.white,
    borderWidth: 1,
    borderColor: colors.mediumGray,
  },
  removeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.darkGray,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: layout.sm,
  },
  submitButton: {
    backgroundColor: colors.primaryBlue,
    borderRadius: layout.borderRadius,
    paddingVertical: layout.md,
    marginHorizontal: layout.screenPadding,
    marginBottom: layout.xl,
    ...shadows.medium,
  },
  submitButtonText: {
    ...typography.button,
    color: colors.white,
    textAlign: 'center',
  },
});

export default WriteReviewScreen;

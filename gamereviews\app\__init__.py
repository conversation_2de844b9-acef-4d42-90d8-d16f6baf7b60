"""
GameReview Professional Flask Application
A well-organized, production-ready Flask app for game reviews
"""

from flask import Flask
from flask_cors import CORS
import os
from .config import Config
from .database import init_database
from .utils.file_handler import setup_directories

def create_app(config_class=Config):
    """Application factory pattern for creating Flask app"""
    
    # Create Flask app
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Enable CORS for React Native
    CORS(app)
    
    # Setup directories and database within app context
    with app.app_context():
        setup_directories()
        init_database()
    
    # Register blueprints
    from .api import api_bp
    from .static_files import static_bp
    
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(static_bp)
    
    # Register error handlers
    from .utils.error_handlers import register_error_handlers
    register_error_handlers(app)
    
    return app

#!/usr/bin/env python3
"""
GameReview Pro - Simple Windows Desktop Application
Guaranteed to work with basic Python installation
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import json
from datetime import datetime

class SimpleGameReviewApp:
    """Simple, reliable game review application"""
    
    def __init__(self):
        print("🎮 Starting GameReview Pro - Simple Edition")
        
        # Initialize database
        self.init_database()
        
        # Create GUI
        self.create_gui()
        
        # Load sample data
        self.load_sample_data()
        
        print("✅ Application ready!")
    
    def init_database(self):
        """Initialize SQLite database"""
        self.conn = sqlite3.connect('simple_gamereviews.db')
        cursor = self.conn.cursor()
        
        # Create games table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS games (
                id INTEGER PRIMARY KEY,
                title TEXT NOT NULL,
                genre TEXT,
                developer TEXT,
                rating REAL DEFAULT 0.0,
                reviews_count INTEGER DEFAULT 0
            )
        ''')
        
        # Create reviews table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS reviews (
                id INTEGER PRIMARY KEY,
                game_id INTEGER,
                reviewer TEXT,
                rating INTEGER,
                review_text TEXT,
                created_at TEXT,
                FOREIGN KEY (game_id) REFERENCES games (id)
            )
        ''')
        
        self.conn.commit()
        print("✅ Database initialized")
    
    def create_gui(self):
        """Create the GUI"""
        self.root = tk.Tk()
        self.root.title("🎮 GameReview Pro - Desktop Edition")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2b2b2b')
        
        # Create notebook for tabs
        style = ttk.Style()
        style.theme_use('clam')
        
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_dashboard_tab()
        self.create_games_tab()
        self.create_reviews_tab()
        self.create_add_review_tab()
        
        print("✅ GUI created")
    
    def create_dashboard_tab(self):
        """Create dashboard tab"""
        dashboard_frame = tk.Frame(self.notebook, bg='#2b2b2b')
        self.notebook.add(dashboard_frame, text="🏠 Dashboard")
        
        # Title
        title_label = tk.Label(
            dashboard_frame,
            text="🎮 GameReview Pro",
            font=('Arial', 24, 'bold'),
            fg='#ffffff',
            bg='#2b2b2b'
        )
        title_label.pack(pady=20)
        
        # Subtitle
        subtitle_label = tk.Label(
            dashboard_frame,
            text="Windows 11 Native Desktop Application",
            font=('Arial', 14),
            fg='#cccccc',
            bg='#2b2b2b'
        )
        subtitle_label.pack(pady=5)
        
        # Stats frame
        stats_frame = tk.Frame(dashboard_frame, bg='#2b2b2b')
        stats_frame.pack(pady=30)
        
        # Get stats
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM games")
        total_games = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM reviews")
        total_reviews = cursor.fetchone()[0]
        
        cursor.execute("SELECT AVG(rating) FROM games WHERE reviews_count > 0")
        avg_rating = cursor.fetchone()[0] or 0.0
        
        # Stats display
        stats_text = f"""
📊 Application Statistics:

🎮 Total Games: {total_games}
📝 Total Reviews: {total_reviews}
⭐ Average Rating: {avg_rating:.1f}/5.0

✅ Features Available:
• Browse game catalog
• Read and write reviews
• Search functionality
• Local database storage
• No internet required

🚀 Ready to use!
        """
        
        stats_label = tk.Label(
            stats_frame,
            text=stats_text.strip(),
            font=('Arial', 12),
            fg='#ffffff',
            bg='#2b2b2b',
            justify=tk.LEFT
        )
        stats_label.pack()
    
    def create_games_tab(self):
        """Create games tab"""
        games_frame = tk.Frame(self.notebook, bg='#2b2b2b')
        self.notebook.add(games_frame, text="🎮 Games")
        
        # Search frame
        search_frame = tk.Frame(games_frame, bg='#2b2b2b')
        search_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(
            search_frame,
            text="🔍 Search Games:",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2b2b2b'
        ).pack(side=tk.LEFT)
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(
            search_frame,
            textvariable=self.search_var,
            font=('Arial', 12),
            width=30
        )
        search_entry.pack(side=tk.LEFT, padx=10)
        
        search_btn = tk.Button(
            search_frame,
            text="Search",
            command=self.search_games,
            bg='#0066ff',
            fg='white',
            font=('Arial', 10, 'bold')
        )
        search_btn.pack(side=tk.LEFT, padx=5)
        
        # Games listbox
        list_frame = tk.Frame(games_frame, bg='#2b2b2b')
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Treeview for games
        columns = ('Title', 'Genre', 'Developer', 'Rating', 'Reviews')
        self.games_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.games_tree.heading(col, text=col)
            self.games_tree.column(col, width=150)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.games_tree.yview)
        self.games_tree.configure(yscrollcommand=scrollbar.set)
        
        self.games_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Load games
        self.load_games()
    
    def create_reviews_tab(self):
        """Create reviews tab"""
        reviews_frame = tk.Frame(self.notebook, bg='#2b2b2b')
        self.notebook.add(reviews_frame, text="📝 Reviews")
        
        # Reviews listbox
        list_frame = tk.Frame(reviews_frame, bg='#2b2b2b')
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Treeview for reviews
        columns = ('Game', 'Reviewer', 'Rating', 'Date')
        self.reviews_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.reviews_tree.heading(col, text=col)
            self.reviews_tree.column(col, width=200)
        
        # Scrollbar
        scrollbar2 = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.reviews_tree.yview)
        self.reviews_tree.configure(yscrollcommand=scrollbar2.set)
        
        self.reviews_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar2.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Load reviews
        self.load_reviews()
    
    def create_add_review_tab(self):
        """Create add review tab"""
        add_frame = tk.Frame(self.notebook, bg='#2b2b2b')
        self.notebook.add(add_frame, text="✍️ Write Review")
        
        # Title
        title_label = tk.Label(
            add_frame,
            text="✍️ Write a Game Review",
            font=('Arial', 18, 'bold'),
            fg='#ffffff',
            bg='#2b2b2b'
        )
        title_label.pack(pady=20)
        
        # Form frame
        form_frame = tk.Frame(add_frame, bg='#3b3b3b')
        form_frame.pack(padx=50, pady=20, fill=tk.X)
        
        # Game selection
        tk.Label(form_frame, text="Game:", bg='#3b3b3b', fg='white', font=('Arial', 12)).grid(row=0, column=0, sticky=tk.W, padx=10, pady=10)
        self.game_var = tk.StringVar()
        self.game_combo = ttk.Combobox(form_frame, textvariable=self.game_var, width=40)
        self.game_combo.grid(row=0, column=1, padx=10, pady=10)
        
        # Reviewer name
        tk.Label(form_frame, text="Your Name:", bg='#3b3b3b', fg='white', font=('Arial', 12)).grid(row=1, column=0, sticky=tk.W, padx=10, pady=10)
        self.reviewer_var = tk.StringVar(value="Anonymous")
        reviewer_entry = tk.Entry(form_frame, textvariable=self.reviewer_var, width=40)
        reviewer_entry.grid(row=1, column=1, padx=10, pady=10)
        
        # Rating
        tk.Label(form_frame, text="Rating:", bg='#3b3b3b', fg='white', font=('Arial', 12)).grid(row=2, column=0, sticky=tk.W, padx=10, pady=10)
        self.rating_var = tk.StringVar(value="5")
        rating_combo = ttk.Combobox(form_frame, textvariable=self.rating_var, values=['1', '2', '3', '4', '5'], width=10)
        rating_combo.grid(row=2, column=1, sticky=tk.W, padx=10, pady=10)
        
        # Review text
        tk.Label(form_frame, text="Review:", bg='#3b3b3b', fg='white', font=('Arial', 12)).grid(row=3, column=0, sticky=tk.NW, padx=10, pady=10)
        self.review_text = tk.Text(form_frame, width=50, height=8)
        self.review_text.grid(row=3, column=1, padx=10, pady=10)
        
        # Submit button
        submit_btn = tk.Button(
            form_frame,
            text="Submit Review",
            command=self.submit_review,
            bg='#00ff00',
            fg='black',
            font=('Arial', 12, 'bold'),
            padx=20,
            pady=5
        )
        submit_btn.grid(row=4, column=1, sticky=tk.W, padx=10, pady=20)
        
        # Load game names
        self.load_game_names()
    
    def load_sample_data(self):
        """Load sample data if database is empty"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM games")
        if cursor.fetchone()[0] > 0:
            return  # Data already exists
        
        # Sample games
        games = [
            ("God of War", "Action/Adventure", "Santa Monica Studio"),
            ("The Witcher 3", "RPG", "CD Projekt Red"),
            ("Elden Ring", "RPG", "FromSoftware"),
            ("Cyberpunk 2077", "RPG", "CD Projekt Red"),
            ("Hades", "Indie", "Supergiant Games"),
            ("Minecraft", "Sandbox", "Mojang"),
            ("Among Us", "Party", "InnerSloth"),
            ("Valorant", "Shooter", "Riot Games"),
            ("FIFA 24", "Sports", "EA Sports"),
            ("Stardew Valley", "Simulation", "ConcernedApe")
        ]
        
        for title, genre, developer in games:
            cursor.execute(
                "INSERT INTO games (title, genre, developer) VALUES (?, ?, ?)",
                (title, genre, developer)
            )
        
        # Sample reviews
        reviews = [
            (1, "GameMaster", 5, "Absolutely incredible game! Amazing story and combat."),
            (1, "ActionFan", 5, "Perfect blend of action and storytelling."),
            (2, "RPGLover", 5, "Best RPG ever made. Incredible world and characters."),
            (3, "SoulsPlayer", 4, "Challenging but rewarding. Great open world design."),
            (4, "CyberGamer", 3, "Good game but had many bugs at launch."),
            (5, "IndieExplorer", 5, "Perfect indie game. Addictive gameplay loop."),
            (6, "Builder", 5, "Endless creativity and fun. Perfect for all ages."),
            (7, "Detective", 4, "Fun social deduction game with friends."),
            (8, "Shooter", 4, "Great competitive shooter with unique abilities."),
            (9, "SportsGamer", 3, "Decent football game but not much innovation."),
            (10, "Farmer", 5, "Relaxing and addictive farming simulation.")
        ]
        
        for game_id, reviewer, rating, text in reviews:
            cursor.execute(
                "INSERT INTO reviews (game_id, reviewer, rating, review_text, created_at) VALUES (?, ?, ?, ?, ?)",
                (game_id, reviewer, rating, text, datetime.now().strftime("%Y-%m-%d"))
            )
        
        # Update game ratings
        cursor.execute('''
            UPDATE games SET 
                rating = (SELECT AVG(rating) FROM reviews WHERE reviews.game_id = games.id),
                reviews_count = (SELECT COUNT(*) FROM reviews WHERE reviews.game_id = games.id)
        ''')
        
        self.conn.commit()
        print("✅ Sample data loaded")
    
    def load_games(self):
        """Load games into treeview"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT title, genre, developer, rating, reviews_count FROM games")
        
        for item in self.games_tree.get_children():
            self.games_tree.delete(item)
        
        for row in cursor.fetchall():
            self.games_tree.insert('', tk.END, values=row)
    
    def load_reviews(self):
        """Load reviews into treeview"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT g.title, r.reviewer, r.rating, r.created_at
            FROM reviews r
            JOIN games g ON r.game_id = g.id
            ORDER BY r.created_at DESC
        ''')
        
        for item in self.reviews_tree.get_children():
            self.reviews_tree.delete(item)
        
        for row in cursor.fetchall():
            self.reviews_tree.insert('', tk.END, values=row)
    
    def load_game_names(self):
        """Load game names for combo box"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT title FROM games")
        games = [row[0] for row in cursor.fetchall()]
        self.game_combo['values'] = games
    
    def search_games(self):
        """Search games"""
        query = self.search_var.get()
        cursor = self.conn.cursor()
        
        if query:
            cursor.execute(
                "SELECT title, genre, developer, rating, reviews_count FROM games WHERE title LIKE ? OR genre LIKE ?",
                (f"%{query}%", f"%{query}%")
            )
        else:
            cursor.execute("SELECT title, genre, developer, rating, reviews_count FROM games")
        
        for item in self.games_tree.get_children():
            self.games_tree.delete(item)
        
        for row in cursor.fetchall():
            self.games_tree.insert('', tk.END, values=row)
    
    def submit_review(self):
        """Submit a new review"""
        game_title = self.game_var.get()
        reviewer = self.reviewer_var.get() or "Anonymous"
        rating = int(self.rating_var.get())
        review_text = self.review_text.get(1.0, tk.END).strip()
        
        if not game_title:
            messagebox.showerror("Error", "Please select a game")
            return
        
        if not review_text:
            messagebox.showerror("Error", "Please write a review")
            return
        
        # Get game ID
        cursor = self.conn.cursor()
        cursor.execute("SELECT id FROM games WHERE title = ?", (game_title,))
        result = cursor.fetchone()
        
        if not result:
            messagebox.showerror("Error", "Game not found")
            return
        
        game_id = result[0]
        
        # Insert review
        cursor.execute(
            "INSERT INTO reviews (game_id, reviewer, rating, review_text, created_at) VALUES (?, ?, ?, ?, ?)",
            (game_id, reviewer, rating, review_text, datetime.now().strftime("%Y-%m-%d"))
        )
        
        # Update game rating
        cursor.execute('''
            UPDATE games SET 
                rating = (SELECT AVG(rating) FROM reviews WHERE reviews.game_id = ?),
                reviews_count = (SELECT COUNT(*) FROM reviews WHERE reviews.game_id = ?)
            WHERE id = ?
        ''', (game_id, game_id, game_id))
        
        self.conn.commit()
        
        # Clear form
        self.game_var.set("")
        self.reviewer_var.set("Anonymous")
        self.rating_var.set("5")
        self.review_text.delete(1.0, tk.END)
        
        # Reload data
        self.load_games()
        self.load_reviews()
        
        messagebox.showinfo("Success", "Review submitted successfully!")
    
    def run(self):
        """Start the application"""
        print("🚀 Starting GameReview Pro...")
        self.root.mainloop()
        print("👋 Application closed")

def main():
    """Main entry point"""
    try:
        app = SimpleGameReviewApp()
        app.run()
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()

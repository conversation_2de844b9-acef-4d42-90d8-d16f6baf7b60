#!/usr/bin/env python3
"""
GameReview Pro - Professional Windows 11 Desktop Application
Swarm 2015 Inspired Design with Black & Blue Theme
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import sqlite3
import json
from datetime import datetime

class ProfessionalGameReviewApp:
    """Professional game review application with Swarm 2015 inspired design"""
    
    # Color Scheme - Swarm 2015 Inspired
    COLORS = {
        'primary_bg': '#0a0a0a',        # Deep black background
        'secondary_bg': '#1a1a1a',      # Dark gray panels
        'card_bg': '#2b2b2b',           # Card backgrounds
        'accent_blue': '#0066ff',       # Electric blue accent
        'accent_blue_hover': '#007acc', # Blue hover state
        'text_primary': '#ffffff',      # White primary text
        'text_secondary': '#cccccc',    # Light gray secondary text
        'text_muted': '#888888',        # Muted text
        'border_blue': '#0066ff',       # Blue borders
        'border_subtle': '#333333',     # Subtle borders
        'success': '#00ff88',           # Success green
        'warning': '#ffaa00',           # Warning orange
        'error': '#ff4444'              # Error red
    }
    
    def __init__(self):
        print("🎮 Starting GameReview Pro - Professional Edition")
        print("🎨 Loading Swarm 2015 inspired design...")
        
        # Initialize database
        self.init_database()
        
        # Create professional GUI
        self.create_professional_gui()
        
        # Load sample data
        self.load_sample_data()

        # Update header stats after loading data
        self.update_header_stats()

        # Show initial dashboard
        self.show_dashboard()

        print("✅ Professional application ready!")
    
    def init_database(self):
        """Initialize SQLite database"""
        self.conn = sqlite3.connect('professional_gamereviews.db')
        cursor = self.conn.cursor()
        
        # Create games table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS games (
                id INTEGER PRIMARY KEY,
                title TEXT NOT NULL,
                genre TEXT,
                developer TEXT,
                rating REAL DEFAULT 0.0,
                reviews_count INTEGER DEFAULT 0,
                description TEXT,
                release_year INTEGER,
                platforms TEXT
            )
        ''')
        
        # Create reviews table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS reviews (
                id INTEGER PRIMARY KEY,
                game_id INTEGER,
                reviewer TEXT,
                rating INTEGER,
                review_text TEXT,
                pros TEXT,
                cons TEXT,
                platform TEXT,
                hours_played REAL,
                created_at TEXT,
                FOREIGN KEY (game_id) REFERENCES games (id)
            )
        ''')
        
        self.conn.commit()
        print("✅ Database initialized")
    
    def create_professional_gui(self):
        """Create professional GUI with Swarm 2015 design"""
        self.root = tk.Tk()
        self.root.title("GameReview Pro - Professional Edition")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)
        self.root.configure(bg=self.COLORS['primary_bg'])
        
        # Configure custom styles
        self.setup_styles()
        
        # Create main layout
        self.create_header()
        self.create_main_layout()
        self.create_status_bar()
        
        print("✅ Professional GUI created")
    
    def setup_styles(self):
        """Setup custom styles for professional appearance"""
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # Configure notebook (tabs) style
        self.style.configure('Professional.TNotebook', 
                           background=self.COLORS['secondary_bg'],
                           borderwidth=0)
        
        self.style.configure('Professional.TNotebook.Tab',
                           background=self.COLORS['card_bg'],
                           foreground=self.COLORS['text_secondary'],
                           padding=[20, 12],
                           borderwidth=1,
                           focuscolor='none')
        
        self.style.map('Professional.TNotebook.Tab',
                      background=[('selected', self.COLORS['accent_blue']),
                                ('active', self.COLORS['accent_blue_hover'])],
                      foreground=[('selected', self.COLORS['text_primary']),
                                ('active', self.COLORS['text_primary'])])
        
        # Configure treeview style
        self.style.configure('Professional.Treeview',
                           background=self.COLORS['card_bg'],
                           foreground=self.COLORS['text_primary'],
                           fieldbackground=self.COLORS['card_bg'],
                           borderwidth=1,
                           relief='solid')
        
        self.style.configure('Professional.Treeview.Heading',
                           background=self.COLORS['accent_blue'],
                           foreground=self.COLORS['text_primary'],
                           borderwidth=1,
                           relief='solid')
        
        # Configure combobox style
        self.style.configure('Professional.TCombobox',
                           fieldbackground=self.COLORS['card_bg'],
                           background=self.COLORS['card_bg'],
                           foreground=self.COLORS['text_primary'],
                           borderwidth=1,
                           relief='solid')
    
    def create_header(self):
        """Create professional header bar"""
        self.header_frame = tk.Frame(self.root, 
                                   bg=self.COLORS['secondary_bg'], 
                                   height=80)
        self.header_frame.pack(fill=tk.X, padx=0, pady=0)
        self.header_frame.pack_propagate(False)
        
        # Application logo and title
        title_frame = tk.Frame(self.header_frame, bg=self.COLORS['secondary_bg'])
        title_frame.pack(side=tk.LEFT, padx=30, pady=20)
        
        # Logo (using emoji for now)
        logo_label = tk.Label(title_frame,
                            text="🎮",
                            font=('Segoe UI', 24),
                            bg=self.COLORS['secondary_bg'],
                            fg=self.COLORS['accent_blue'])
        logo_label.pack(side=tk.LEFT, padx=(0, 15))
        
        # Title
        title_label = tk.Label(title_frame,
                             text="GameReview Pro",
                             font=('Segoe UI', 20, 'bold'),
                             bg=self.COLORS['secondary_bg'],
                             fg=self.COLORS['text_primary'])
        title_label.pack(side=tk.LEFT)
        
        # Subtitle
        subtitle_label = tk.Label(title_frame,
                                text="Professional Edition",
                                font=('Segoe UI', 12),
                                bg=self.COLORS['secondary_bg'],
                                fg=self.COLORS['text_secondary'])
        subtitle_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Header stats (right side)
        stats_frame = tk.Frame(self.header_frame, bg=self.COLORS['secondary_bg'])
        stats_frame.pack(side=tk.RIGHT, padx=30, pady=20)
        
        # Quick stats will be populated later
        self.header_stats_label = tk.Label(stats_frame,
                                         text="Loading...",
                                         font=('Segoe UI', 11),
                                         bg=self.COLORS['secondary_bg'],
                                         fg=self.COLORS['text_secondary'])
        self.header_stats_label.pack()
    
    def create_main_layout(self):
        """Create main application layout"""
        # Main container
        self.main_container = tk.Frame(self.root, bg=self.COLORS['primary_bg'])
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
        
        # Create sidebar navigation
        self.create_sidebar()
        
        # Create content area
        self.create_content_area()
    
    def create_sidebar(self):
        """Create professional sidebar navigation"""
        self.sidebar = tk.Frame(self.main_container, 
                              bg=self.COLORS['secondary_bg'], 
                              width=280)
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y, padx=0, pady=0)
        self.sidebar.pack_propagate(False)
        
        # Navigation title
        nav_title = tk.Label(self.sidebar,
                           text="NAVIGATION",
                           font=('Segoe UI', 10, 'bold'),
                           bg=self.COLORS['secondary_bg'],
                           fg=self.COLORS['text_muted'])
        nav_title.pack(anchor=tk.W, padx=30, pady=(30, 20))
        
        # Navigation buttons
        self.nav_buttons = {}
        nav_items = [
            ("🏠", "Dashboard", self.show_dashboard),
            ("🎮", "Games Library", self.show_games),
            ("📝", "Reviews", self.show_reviews),
            ("✍️", "Write Review", self.show_write_review),
            ("🔍", "Search", self.show_search),
            ("📊", "Analytics", self.show_analytics)
        ]
        
        for icon, text, command in nav_items:
            self.create_nav_button(icon, text, command)
        
        # Sidebar footer
        footer_frame = tk.Frame(self.sidebar, bg=self.COLORS['secondary_bg'])
        footer_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=30, pady=30)
        
        footer_text = tk.Label(footer_frame,
                             text="Professional Edition\nv2.0 • Windows 11",
                             font=('Segoe UI', 9),
                             bg=self.COLORS['secondary_bg'],
                             fg=self.COLORS['text_muted'],
                             justify=tk.CENTER)
        footer_text.pack()
    
    def create_nav_button(self, icon, text, command):
        """Create a professional navigation button"""
        btn_frame = tk.Frame(self.sidebar, bg=self.COLORS['secondary_bg'])
        btn_frame.pack(fill=tk.X, padx=20, pady=2)
        
        # Create button with hover effects
        btn = tk.Button(btn_frame,
                       text=f"{icon}  {text}",
                       font=('Segoe UI', 12),
                       bg=self.COLORS['secondary_bg'],
                       fg=self.COLORS['text_secondary'],
                       activebackground=self.COLORS['accent_blue'],
                       activeforeground=self.COLORS['text_primary'],
                       relief=tk.FLAT,
                       anchor=tk.W,
                       padx=20,
                       pady=12,
                       command=command,
                       cursor='hand2')
        btn.pack(fill=tk.X)
        
        # Hover effects
        def on_enter(e):
            btn.configure(bg=self.COLORS['accent_blue'], 
                         fg=self.COLORS['text_primary'])
        
        def on_leave(e):
            btn.configure(bg=self.COLORS['secondary_bg'], 
                         fg=self.COLORS['text_secondary'])
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        
        self.nav_buttons[text] = btn
    
    def create_content_area(self):
        """Create main content area"""
        self.content_frame = tk.Frame(self.main_container, bg=self.COLORS['primary_bg'])
        self.content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=0, pady=0)
        
        # Initialize current view
        self.current_view = None
    
    def create_status_bar(self):
        """Create professional status bar"""
        self.status_bar = tk.Frame(self.root, 
                                 bg=self.COLORS['secondary_bg'], 
                                 height=35)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_bar.pack_propagate(False)
        
        # Status text
        self.status_label = tk.Label(self.status_bar,
                                   text="Ready",
                                   font=('Segoe UI', 10),
                                   bg=self.COLORS['secondary_bg'],
                                   fg=self.COLORS['text_secondary'])
        self.status_label.pack(side=tk.LEFT, padx=20, pady=8)
        
        # Database status
        self.db_status_label = tk.Label(self.status_bar,
                                      text="Database: Connected",
                                      font=('Segoe UI', 10),
                                      bg=self.COLORS['secondary_bg'],
                                      fg=self.COLORS['success'])
        self.db_status_label.pack(side=tk.RIGHT, padx=20, pady=8)
    
    def clear_content(self):
        """Clear current content"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def create_professional_card(self, parent, title=None, padding=20):
        """Create a professional card with blue accent border"""
        card = tk.Frame(parent, 
                       bg=self.COLORS['card_bg'],
                       relief=tk.SOLID,
                       borderwidth=1)
        card.configure(highlightbackground=self.COLORS['border_blue'],
                      highlightthickness=1)
        
        if title:
            title_label = tk.Label(card,
                                 text=title,
                                 font=('Segoe UI', 14, 'bold'),
                                 bg=self.COLORS['card_bg'],
                                 fg=self.COLORS['text_primary'])
            title_label.pack(anchor=tk.W, padx=padding, pady=(padding, 10))
        
        return card
    
    def create_professional_button(self, parent, text, command, style='primary'):
        """Create a professional button with gradient effect"""
        if style == 'primary':
            bg_color = self.COLORS['accent_blue']
            hover_color = self.COLORS['accent_blue_hover']
            text_color = self.COLORS['text_primary']
        else:
            bg_color = self.COLORS['card_bg']
            hover_color = self.COLORS['border_subtle']
            text_color = self.COLORS['text_secondary']
        
        btn = tk.Button(parent,
                       text=text,
                       font=('Segoe UI', 11, 'bold'),
                       bg=bg_color,
                       fg=text_color,
                       activebackground=hover_color,
                       activeforeground=self.COLORS['text_primary'],
                       relief=tk.FLAT,
                       padx=20,
                       pady=10,
                       command=command,
                       cursor='hand2')
        
        # Hover effects
        def on_enter(e):
            btn.configure(bg=hover_color)
        
        def on_leave(e):
            btn.configure(bg=bg_color)
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        
        return btn

    def show_dashboard(self):
        """Show professional dashboard"""
        self.clear_content()
        self.update_nav_selection("Dashboard")

        # Main dashboard container
        dashboard = tk.Frame(self.content_frame, bg=self.COLORS['primary_bg'])
        dashboard.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # Welcome section
        welcome_card = self.create_professional_card(dashboard, padding=30)
        welcome_card.pack(fill=tk.X, pady=(0, 20))

        welcome_title = tk.Label(welcome_card,
                               text="Welcome to GameReview Pro",
                               font=('Segoe UI', 24, 'bold'),
                               bg=self.COLORS['card_bg'],
                               fg=self.COLORS['text_primary'])
        welcome_title.pack(anchor=tk.W, padx=30, pady=(30, 10))

        welcome_subtitle = tk.Label(welcome_card,
                                   text="Professional game review platform with advanced analytics",
                                   font=('Segoe UI', 12),
                                   bg=self.COLORS['card_bg'],
                                   fg=self.COLORS['text_secondary'])
        welcome_subtitle.pack(anchor=tk.W, padx=30, pady=(0, 30))

        # Statistics cards
        stats_frame = tk.Frame(dashboard, bg=self.COLORS['primary_bg'])
        stats_frame.pack(fill=tk.X, pady=(0, 20))

        # Get statistics
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM games")
        total_games = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM reviews")
        total_reviews = cursor.fetchone()[0]

        cursor.execute("SELECT AVG(rating) FROM games WHERE reviews_count > 0")
        avg_rating = cursor.fetchone()[0] or 0.0

        # Update header stats
        self.update_header_stats()

        # Create stat cards
        stats_data = [
            ("🎮", "Total Games", str(total_games), "games in library"),
            ("📝", "Reviews", str(total_reviews), "user reviews"),
            ("⭐", "Average Rating", f"{avg_rating:.1f}/5", "overall score"),
            ("🏆", "Top Genre", self.get_top_genre(), "most popular")
        ]

        for i, (icon, title, value, subtitle) in enumerate(stats_data):
            stat_card = self.create_stat_card(stats_frame, icon, title, value, subtitle)
            stat_card.grid(row=0, column=i, padx=10, sticky="ew")
            stats_frame.grid_columnconfigure(i, weight=1)

        # Recent activity section
        activity_card = self.create_professional_card(dashboard, "Recent Activity", 25)
        activity_card.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        self.create_recent_activity(activity_card)

        self.status_label.configure(text="Dashboard loaded")

    def create_stat_card(self, parent, icon, title, value, subtitle):
        """Create a professional statistics card"""
        card = tk.Frame(parent,
                       bg=self.COLORS['card_bg'],
                       relief=tk.SOLID,
                       borderwidth=1)
        card.configure(highlightbackground=self.COLORS['border_blue'],
                      highlightthickness=1)

        # Icon
        icon_label = tk.Label(card,
                            text=icon,
                            font=('Segoe UI', 32),
                            bg=self.COLORS['card_bg'],
                            fg=self.COLORS['accent_blue'])
        icon_label.pack(pady=(20, 10))

        # Value
        value_label = tk.Label(card,
                             text=value,
                             font=('Segoe UI', 20, 'bold'),
                             bg=self.COLORS['card_bg'],
                             fg=self.COLORS['text_primary'])
        value_label.pack()

        # Title
        title_label = tk.Label(card,
                             text=title,
                             font=('Segoe UI', 12, 'bold'),
                             bg=self.COLORS['card_bg'],
                             fg=self.COLORS['text_secondary'])
        title_label.pack(pady=(5, 0))

        # Subtitle
        subtitle_label = tk.Label(card,
                                text=subtitle,
                                font=('Segoe UI', 10),
                                bg=self.COLORS['card_bg'],
                                fg=self.COLORS['text_muted'])
        subtitle_label.pack(pady=(0, 20))

        return card

    def create_recent_activity(self, parent):
        """Create recent activity section"""
        # Get recent reviews
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT g.title, r.reviewer, r.rating, r.created_at, r.review_text
            FROM reviews r
            JOIN games g ON r.game_id = g.id
            ORDER BY r.created_at DESC
            LIMIT 5
        ''')

        recent_reviews = cursor.fetchall()

        if not recent_reviews:
            no_activity = tk.Label(parent,
                                 text="No recent activity. Start by writing your first review!",
                                 font=('Segoe UI', 12),
                                 bg=self.COLORS['card_bg'],
                                 fg=self.COLORS['text_muted'])
            no_activity.pack(padx=25, pady=20)
            return

        for i, (game_title, reviewer, rating, date, review_text) in enumerate(recent_reviews):
            activity_item = tk.Frame(parent, bg=self.COLORS['card_bg'])
            activity_item.pack(fill=tk.X, padx=25, pady=8)

            # Rating stars
            stars = "⭐" * rating
            rating_label = tk.Label(activity_item,
                                  text=stars,
                                  font=('Segoe UI', 12),
                                  bg=self.COLORS['card_bg'],
                                  fg=self.COLORS['accent_blue'])
            rating_label.pack(side=tk.LEFT)

            # Review info
            info_text = f"{game_title} • {reviewer} • {date}"
            info_label = tk.Label(activity_item,
                                text=info_text,
                                font=('Segoe UI', 11),
                                bg=self.COLORS['card_bg'],
                                fg=self.COLORS['text_primary'])
            info_label.pack(side=tk.LEFT, padx=(10, 0))

            # Review preview
            if review_text:
                preview = review_text[:50] + "..." if len(review_text) > 50 else review_text
                preview_label = tk.Label(activity_item,
                                       text=f'"{preview}"',
                                       font=('Segoe UI', 10, 'italic'),
                                       bg=self.COLORS['card_bg'],
                                       fg=self.COLORS['text_secondary'])
                preview_label.pack(side=tk.RIGHT)

    def get_top_genre(self):
        """Get the most popular genre"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT genre, COUNT(*) as count
            FROM games
            WHERE genre IS NOT NULL
            GROUP BY genre
            ORDER BY count DESC
            LIMIT 1
        ''')
        result = cursor.fetchone()
        return result[0] if result else "Unknown"

    def update_header_stats(self):
        """Update header statistics"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM games")
        total_games = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM reviews")
        total_reviews = cursor.fetchone()[0]

        cursor.execute("SELECT AVG(rating) FROM games WHERE reviews_count > 0")
        avg_rating = cursor.fetchone()[0] or 0.0

        if hasattr(self, 'header_stats_label'):
            self.header_stats_label.configure(
                text=f"📊 {total_games} Games • {total_reviews} Reviews • ⭐ {avg_rating:.1f} Avg"
            )

    def update_nav_selection(self, selected):
        """Update navigation button selection"""
        for name, btn in self.nav_buttons.items():
            if name == selected:
                btn.configure(bg=self.COLORS['accent_blue'],
                            fg=self.COLORS['text_primary'])
            else:
                btn.configure(bg=self.COLORS['secondary_bg'],
                            fg=self.COLORS['text_secondary'])

    def show_games(self):
        """Show professional games library"""
        self.clear_content()
        self.update_nav_selection("Games Library")

        # Main games container
        games_container = tk.Frame(self.content_frame, bg=self.COLORS['primary_bg'])
        games_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # Header with search
        header_card = self.create_professional_card(games_container, padding=25)
        header_card.pack(fill=tk.X, pady=(0, 20))

        header_frame = tk.Frame(header_card, bg=self.COLORS['card_bg'])
        header_frame.pack(fill=tk.X, padx=25, pady=25)

        # Title
        title_label = tk.Label(header_frame,
                             text="Games Library",
                             font=('Segoe UI', 20, 'bold'),
                             bg=self.COLORS['card_bg'],
                             fg=self.COLORS['text_primary'])
        title_label.pack(side=tk.LEFT)

        # Search frame
        search_frame = tk.Frame(header_frame, bg=self.COLORS['card_bg'])
        search_frame.pack(side=tk.RIGHT)

        # Search entry
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame,
                              textvariable=self.search_var,
                              font=('Segoe UI', 11),
                              bg=self.COLORS['secondary_bg'],
                              fg=self.COLORS['text_primary'],
                              insertbackground=self.COLORS['text_primary'],
                              relief=tk.FLAT,
                              width=25)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))

        # Search button
        search_btn = self.create_professional_button(search_frame, "🔍 Search", self.search_games)
        search_btn.pack(side=tk.LEFT)

        # Games grid
        games_card = self.create_professional_card(games_container, "Game Collection", 25)
        games_card.pack(fill=tk.BOTH, expand=True)

        # Scrollable frame for games
        canvas = tk.Canvas(games_card, bg=self.COLORS['card_bg'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(games_card, orient="vertical", command=canvas.yview)
        self.games_scrollable = tk.Frame(canvas, bg=self.COLORS['card_bg'])

        self.games_scrollable.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.games_scrollable, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=25, pady=25)
        scrollbar.pack(side="right", fill="y")

        # Load games
        self.load_games_grid()

        self.status_label.configure(text="Games library loaded")

    def load_games_grid(self):
        """Load games in a professional grid layout"""
        # Clear existing games
        for widget in self.games_scrollable.winfo_children():
            widget.destroy()

        # Get games from database
        cursor = self.conn.cursor()
        query = "SELECT id, title, genre, developer, rating, reviews_count, description FROM games"
        search_term = self.search_var.get().strip()

        if search_term:
            query += " WHERE title LIKE ? OR genre LIKE ? OR developer LIKE ?"
            cursor.execute(query, (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"))
        else:
            cursor.execute(query)

        games = cursor.fetchall()

        # Display games in grid (2 columns)
        for i, game in enumerate(games):
            row = i // 2
            col = i % 2

            game_card = self.create_game_card(self.games_scrollable, game)
            game_card.grid(row=row, column=col, padx=10, pady=10, sticky="ew")

        # Configure grid weights
        self.games_scrollable.grid_columnconfigure(0, weight=1)
        self.games_scrollable.grid_columnconfigure(1, weight=1)

    def create_game_card(self, parent, game):
        """Create a professional game card"""
        game_id, title, genre, developer, rating, reviews_count, description = game

        # Main card frame
        card = tk.Frame(parent,
                       bg=self.COLORS['secondary_bg'],
                       relief=tk.SOLID,
                       borderwidth=1)
        card.configure(highlightbackground=self.COLORS['border_blue'],
                      highlightthickness=1)

        # Card content
        content_frame = tk.Frame(card, bg=self.COLORS['secondary_bg'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Game title
        title_label = tk.Label(content_frame,
                             text=title,
                             font=('Segoe UI', 14, 'bold'),
                             bg=self.COLORS['secondary_bg'],
                             fg=self.COLORS['text_primary'])
        title_label.pack(anchor=tk.W)

        # Genre and developer
        meta_text = f"{genre} • {developer}"
        meta_label = tk.Label(content_frame,
                            text=meta_text,
                            font=('Segoe UI', 10),
                            bg=self.COLORS['secondary_bg'],
                            fg=self.COLORS['text_secondary'])
        meta_label.pack(anchor=tk.W, pady=(2, 8))

        # Rating
        if rating > 0:
            stars = "⭐" * int(rating)
            rating_text = f"{stars} {rating:.1f}/5 ({reviews_count} reviews)"
        else:
            rating_text = "No reviews yet"

        rating_label = tk.Label(content_frame,
                              text=rating_text,
                              font=('Segoe UI', 11),
                              bg=self.COLORS['secondary_bg'],
                              fg=self.COLORS['accent_blue'])
        rating_label.pack(anchor=tk.W, pady=(0, 8))

        # Description
        if description:
            desc_text = description[:100] + "..." if len(description) > 100 else description
            desc_label = tk.Label(content_frame,
                                text=desc_text,
                                font=('Segoe UI', 10),
                                bg=self.COLORS['secondary_bg'],
                                fg=self.COLORS['text_secondary'],
                                wraplength=300,
                                justify=tk.LEFT)
            desc_label.pack(anchor=tk.W, pady=(0, 15))

        # Action buttons
        btn_frame = tk.Frame(content_frame, bg=self.COLORS['secondary_bg'])
        btn_frame.pack(anchor=tk.W)

        view_btn = self.create_professional_button(btn_frame, "View Reviews",
                                                 lambda: self.view_game_reviews(game_id, title))
        view_btn.pack(side=tk.LEFT, padx=(0, 10))

        review_btn = self.create_professional_button(btn_frame, "Write Review",
                                                   lambda: self.write_review_for_game(game_id, title),
                                                   style='secondary')
        review_btn.pack(side=tk.LEFT)

        return card

    def search_games(self):
        """Search games and refresh grid"""
        self.load_games_grid()
        search_term = self.search_var.get().strip()
        if search_term:
            self.status_label.configure(text=f"Search results for: {search_term}")
        else:
            self.status_label.configure(text="Showing all games")

    def view_game_reviews(self, game_id, game_title):
        """View reviews for a specific game"""
        self.selected_game_id = game_id
        self.selected_game_title = game_title
        self.show_reviews()

    def write_review_for_game(self, game_id, game_title):
        """Write a review for a specific game"""
        self.selected_game_id = game_id
        self.selected_game_title = game_title
        self.show_write_review()

    def show_reviews(self):
        """Show professional reviews view"""
        self.clear_content()
        self.update_nav_selection("Reviews")

        # Main reviews container
        reviews_container = tk.Frame(self.content_frame, bg=self.COLORS['primary_bg'])
        reviews_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # Header
        header_card = self.create_professional_card(reviews_container, padding=25)
        header_card.pack(fill=tk.X, pady=(0, 20))

        header_frame = tk.Frame(header_card, bg=self.COLORS['card_bg'])
        header_frame.pack(fill=tk.X, padx=25, pady=25)

        # Title
        if hasattr(self, 'selected_game_title'):
            title_text = f"Reviews for {self.selected_game_title}"
        else:
            title_text = "All Reviews"

        title_label = tk.Label(header_frame,
                             text=title_text,
                             font=('Segoe UI', 20, 'bold'),
                             bg=self.COLORS['card_bg'],
                             fg=self.COLORS['text_primary'])
        title_label.pack(side=tk.LEFT)

        # Back button if viewing specific game
        if hasattr(self, 'selected_game_title'):
            back_btn = self.create_professional_button(header_frame, "← Back to All Reviews",
                                                     self.show_all_reviews, style='secondary')
            back_btn.pack(side=tk.RIGHT)

        # Reviews list
        reviews_card = self.create_professional_card(reviews_container, "User Reviews", 25)
        reviews_card.pack(fill=tk.BOTH, expand=True)

        # Scrollable frame for reviews
        canvas = tk.Canvas(reviews_card, bg=self.COLORS['card_bg'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(reviews_card, orient="vertical", command=canvas.yview)
        self.reviews_scrollable = tk.Frame(canvas, bg=self.COLORS['card_bg'])

        self.reviews_scrollable.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.reviews_scrollable, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=25, pady=25)
        scrollbar.pack(side="right", fill="y")

        # Load reviews
        self.load_reviews_list()

        self.status_label.configure(text="Reviews loaded")

    def load_reviews_list(self):
        """Load reviews in professional list format"""
        # Clear existing reviews
        for widget in self.reviews_scrollable.winfo_children():
            widget.destroy()

        # Get reviews from database
        cursor = self.conn.cursor()
        if hasattr(self, 'selected_game_id'):
            cursor.execute('''
                SELECT r.id, g.title, r.reviewer, r.rating, r.review_text,
                       r.pros, r.cons, r.platform, r.hours_played, r.created_at
                FROM reviews r
                JOIN games g ON r.game_id = g.id
                WHERE r.game_id = ?
                ORDER BY r.created_at DESC
            ''', (self.selected_game_id,))
        else:
            cursor.execute('''
                SELECT r.id, g.title, r.reviewer, r.rating, r.review_text,
                       r.pros, r.cons, r.platform, r.hours_played, r.created_at
                FROM reviews r
                JOIN games g ON r.game_id = g.id
                ORDER BY r.created_at DESC
            ''')

        reviews = cursor.fetchall()

        if not reviews:
            no_reviews = tk.Label(self.reviews_scrollable,
                                text="No reviews found. Be the first to write one!",
                                font=('Segoe UI', 14),
                                bg=self.COLORS['card_bg'],
                                fg=self.COLORS['text_muted'])
            no_reviews.pack(pady=50)
            return

        # Display reviews
        for review in reviews:
            review_card = self.create_review_card(self.reviews_scrollable, review)
            review_card.pack(fill=tk.X, padx=10, pady=10)

    def create_review_card(self, parent, review):
        """Create a professional review card"""
        review_id, game_title, reviewer, rating, review_text, pros, cons, platform, hours_played, created_at = review

        # Main card frame
        card = tk.Frame(parent,
                       bg=self.COLORS['secondary_bg'],
                       relief=tk.SOLID,
                       borderwidth=1)
        card.configure(highlightbackground=self.COLORS['border_blue'],
                      highlightthickness=1)

        # Card content
        content_frame = tk.Frame(card, bg=self.COLORS['secondary_bg'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=25, pady=20)

        # Header with game title and rating
        header_frame = tk.Frame(content_frame, bg=self.COLORS['secondary_bg'])
        header_frame.pack(fill=tk.X, pady=(0, 10))

        # Game title (if showing all reviews)
        if not hasattr(self, 'selected_game_id'):
            game_label = tk.Label(header_frame,
                                text=game_title,
                                font=('Segoe UI', 14, 'bold'),
                                bg=self.COLORS['secondary_bg'],
                                fg=self.COLORS['text_primary'])
            game_label.pack(side=tk.LEFT)

        # Rating stars
        stars = "⭐" * rating
        rating_label = tk.Label(header_frame,
                              text=f"{stars} {rating}/5",
                              font=('Segoe UI', 12),
                              bg=self.COLORS['secondary_bg'],
                              fg=self.COLORS['accent_blue'])
        rating_label.pack(side=tk.RIGHT)

        # Reviewer info
        meta_text = f"By {reviewer}"
        if platform:
            meta_text += f" on {platform}"
        if hours_played and hours_played > 0:
            meta_text += f" • {hours_played} hours played"
        meta_text += f" • {created_at}"

        meta_label = tk.Label(content_frame,
                            text=meta_text,
                            font=('Segoe UI', 10),
                            bg=self.COLORS['secondary_bg'],
                            fg=self.COLORS['text_secondary'])
        meta_label.pack(anchor=tk.W, pady=(0, 10))

        # Review text
        if review_text:
            text_label = tk.Label(content_frame,
                                text=review_text,
                                font=('Segoe UI', 11),
                                bg=self.COLORS['secondary_bg'],
                                fg=self.COLORS['text_primary'],
                                wraplength=600,
                                justify=tk.LEFT)
            text_label.pack(anchor=tk.W, pady=(0, 15))

        # Pros and cons
        if pros or cons:
            pros_cons_frame = tk.Frame(content_frame, bg=self.COLORS['secondary_bg'])
            pros_cons_frame.pack(fill=tk.X, pady=(0, 10))

            if pros:
                pros_frame = tk.Frame(pros_cons_frame, bg=self.COLORS['secondary_bg'])
                pros_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

                pros_title = tk.Label(pros_frame,
                                    text="👍 Pros:",
                                    font=('Segoe UI', 11, 'bold'),
                                    bg=self.COLORS['secondary_bg'],
                                    fg=self.COLORS['success'])
                pros_title.pack(anchor=tk.W)

                try:
                    pros_list = json.loads(pros) if pros else []
                    for pro in pros_list:
                        pro_label = tk.Label(pros_frame,
                                           text=f"• {pro}",
                                           font=('Segoe UI', 10),
                                           bg=self.COLORS['secondary_bg'],
                                           fg=self.COLORS['text_secondary'])
                        pro_label.pack(anchor=tk.W, padx=(10, 0))
                except:
                    pass

            if cons:
                cons_frame = tk.Frame(pros_cons_frame, bg=self.COLORS['secondary_bg'])
                cons_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

                cons_title = tk.Label(cons_frame,
                                    text="👎 Cons:",
                                    font=('Segoe UI', 11, 'bold'),
                                    bg=self.COLORS['secondary_bg'],
                                    fg=self.COLORS['error'])
                cons_title.pack(anchor=tk.W)

                try:
                    cons_list = json.loads(cons) if cons else []
                    for con in cons_list:
                        con_label = tk.Label(cons_frame,
                                           text=f"• {con}",
                                           font=('Segoe UI', 10),
                                           bg=self.COLORS['secondary_bg'],
                                           fg=self.COLORS['text_secondary'])
                        con_label.pack(anchor=tk.W, padx=(10, 0))
                except:
                    pass

        return card

    def show_all_reviews(self):
        """Show all reviews (clear game selection)"""
        if hasattr(self, 'selected_game_id'):
            delattr(self, 'selected_game_id')
        if hasattr(self, 'selected_game_title'):
            delattr(self, 'selected_game_title')
        self.show_reviews()

    def show_write_review(self):
        """Show professional write review form"""
        self.clear_content()
        self.update_nav_selection("Write Review")

        # Main form container
        form_container = tk.Frame(self.content_frame, bg=self.COLORS['primary_bg'])
        form_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # Header
        header_card = self.create_professional_card(form_container, padding=25)
        header_card.pack(fill=tk.X, pady=(0, 20))

        header_frame = tk.Frame(header_card, bg=self.COLORS['card_bg'])
        header_frame.pack(fill=tk.X, padx=25, pady=25)

        title_label = tk.Label(header_frame,
                             text="Write a Review",
                             font=('Segoe UI', 20, 'bold'),
                             bg=self.COLORS['card_bg'],
                             fg=self.COLORS['text_primary'])
        title_label.pack(side=tk.LEFT)

        # Form card
        form_card = self.create_professional_card(form_container, "Review Details", 30)
        form_card.pack(fill=tk.BOTH, expand=True)

        # Scrollable form
        canvas = tk.Canvas(form_card, bg=self.COLORS['card_bg'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(form_card, orient="vertical", command=canvas.yview)
        form_frame = tk.Frame(canvas, bg=self.COLORS['card_bg'])

        form_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=form_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=30, pady=30)
        scrollbar.pack(side="right", fill="y")

        # Form fields
        self.create_review_form(form_frame)

        self.status_label.configure(text="Write review form loaded")

    def create_review_form(self, parent):
        """Create the review form with professional styling"""
        # Game selection
        self.create_form_field(parent, "Game", "game_selection")

        # Get games for dropdown
        cursor = self.conn.cursor()
        cursor.execute("SELECT id, title FROM games ORDER BY title")
        games = cursor.fetchall()
        game_options = [f"{title}" for _, title in games]

        self.game_var = tk.StringVar()
        if hasattr(self, 'selected_game_title'):
            self.game_var.set(self.selected_game_title)

        game_combo = ttk.Combobox(parent,
                                textvariable=self.game_var,
                                values=game_options,
                                style='Professional.TCombobox',
                                font=('Segoe UI', 11),
                                width=40)
        game_combo.pack(anchor=tk.W, padx=30, pady=(0, 20))

        # Reviewer name
        self.create_form_field(parent, "Your Name", "reviewer_name")
        self.reviewer_var = tk.StringVar(value="Anonymous Reviewer")
        reviewer_entry = tk.Entry(parent,
                                textvariable=self.reviewer_var,
                                font=('Segoe UI', 11),
                                bg=self.COLORS['secondary_bg'],
                                fg=self.COLORS['text_primary'],
                                insertbackground=self.COLORS['text_primary'],
                                relief=tk.FLAT,
                                width=40)
        reviewer_entry.pack(anchor=tk.W, padx=30, pady=(0, 20))

        # Rating
        self.create_form_field(parent, "Rating", "rating")
        rating_frame = tk.Frame(parent, bg=self.COLORS['card_bg'])
        rating_frame.pack(anchor=tk.W, padx=30, pady=(0, 20))

        self.rating_var = tk.StringVar(value="5")
        for i in range(1, 6):
            rating_btn = tk.Radiobutton(rating_frame,
                                      text=f"{'⭐' * i} {i}/5",
                                      variable=self.rating_var,
                                      value=str(i),
                                      font=('Segoe UI', 11),
                                      bg=self.COLORS['card_bg'],
                                      fg=self.COLORS['text_primary'],
                                      selectcolor=self.COLORS['accent_blue'],
                                      activebackground=self.COLORS['card_bg'],
                                      activeforeground=self.COLORS['text_primary'])
            rating_btn.pack(anchor=tk.W, pady=2)

        # Platform
        self.create_form_field(parent, "Platform", "platform")
        platforms = ["PC", "PlayStation 5", "PlayStation 4", "Xbox Series X/S", "Xbox One", "Nintendo Switch", "Mobile"]
        self.platform_var = tk.StringVar(value="PC")
        platform_combo = ttk.Combobox(parent,
                                    textvariable=self.platform_var,
                                    values=platforms,
                                    style='Professional.TCombobox',
                                    font=('Segoe UI', 11),
                                    width=25)
        platform_combo.pack(anchor=tk.W, padx=30, pady=(0, 20))

        # Hours played
        self.create_form_field(parent, "Hours Played", "hours_played")
        self.hours_var = tk.StringVar(value="0")
        hours_entry = tk.Entry(parent,
                             textvariable=self.hours_var,
                             font=('Segoe UI', 11),
                             bg=self.COLORS['secondary_bg'],
                             fg=self.COLORS['text_primary'],
                             insertbackground=self.COLORS['text_primary'],
                             relief=tk.FLAT,
                             width=15)
        hours_entry.pack(anchor=tk.W, padx=30, pady=(0, 20))

        # Review text
        self.create_form_field(parent, "Review", "review_text")
        self.review_text = tk.Text(parent,
                                 font=('Segoe UI', 11),
                                 bg=self.COLORS['secondary_bg'],
                                 fg=self.COLORS['text_primary'],
                                 insertbackground=self.COLORS['text_primary'],
                                 relief=tk.FLAT,
                                 width=70,
                                 height=8,
                                 wrap=tk.WORD)
        self.review_text.pack(anchor=tk.W, padx=30, pady=(0, 20))

        # Pros
        self.create_form_field(parent, "Pros (one per line)", "pros")
        self.pros_text = tk.Text(parent,
                               font=('Segoe UI', 11),
                               bg=self.COLORS['secondary_bg'],
                               fg=self.COLORS['text_primary'],
                               insertbackground=self.COLORS['text_primary'],
                               relief=tk.FLAT,
                               width=70,
                               height=4,
                               wrap=tk.WORD)
        self.pros_text.pack(anchor=tk.W, padx=30, pady=(0, 20))

        # Cons
        self.create_form_field(parent, "Cons (one per line)", "cons")
        self.cons_text = tk.Text(parent,
                               font=('Segoe UI', 11),
                               bg=self.COLORS['secondary_bg'],
                               fg=self.COLORS['text_primary'],
                               insertbackground=self.COLORS['text_primary'],
                               relief=tk.FLAT,
                               width=70,
                               height=4,
                               wrap=tk.WORD)
        self.cons_text.pack(anchor=tk.W, padx=30, pady=(0, 30))

        # Submit button
        submit_btn = self.create_professional_button(parent, "Submit Review", self.submit_review)
        submit_btn.pack(anchor=tk.W, padx=30, pady=(0, 30))

    def create_form_field(self, parent, label_text, field_name):
        """Create a form field label"""
        label = tk.Label(parent,
                       text=label_text,
                       font=('Segoe UI', 12, 'bold'),
                       bg=self.COLORS['card_bg'],
                       fg=self.COLORS['text_primary'])
        label.pack(anchor=tk.W, padx=30, pady=(20, 5))
        return label

    def submit_review(self):
        """Submit the review with professional validation"""
        try:
            # Validate form data
            game_title = self.game_var.get().strip()
            if not game_title:
                messagebox.showerror("Validation Error", "Please select a game")
                return

            reviewer = self.reviewer_var.get().strip() or "Anonymous Reviewer"
            rating = int(self.rating_var.get())
            platform = self.platform_var.get()

            try:
                hours_played = float(self.hours_var.get()) if self.hours_var.get() else 0.0
            except ValueError:
                hours_played = 0.0

            review_text = self.review_text.get("1.0", "end-1c").strip()

            # Parse pros and cons
            pros_text = self.pros_text.get("1.0", "end-1c").strip()
            pros = [line.strip() for line in pros_text.split('\n') if line.strip()]

            cons_text = self.cons_text.get("1.0", "end-1c").strip()
            cons = [line.strip() for line in cons_text.split('\n') if line.strip()]

            # Get game ID
            cursor = self.conn.cursor()
            cursor.execute("SELECT id FROM games WHERE title = ?", (game_title,))
            result = cursor.fetchone()

            if not result:
                messagebox.showerror("Error", "Selected game not found")
                return

            game_id = result[0]

            # Insert review
            cursor.execute('''
                INSERT INTO reviews (game_id, reviewer, rating, review_text, pros, cons,
                                   platform, hours_played, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (game_id, reviewer, rating, review_text, json.dumps(pros),
                  json.dumps(cons), platform, hours_played, datetime.now().strftime("%Y-%m-%d")))

            # Update game statistics
            cursor.execute('''
                UPDATE games SET
                    rating = (SELECT AVG(rating) FROM reviews WHERE reviews.game_id = ?),
                    reviews_count = (SELECT COUNT(*) FROM reviews WHERE reviews.game_id = ?)
                WHERE id = ?
            ''', (game_id, game_id, game_id))

            self.conn.commit()

            # Show success message
            messagebox.showinfo("Success", "Review submitted successfully!")

            # Clear form
            self.game_var.set("")
            self.reviewer_var.set("Anonymous Reviewer")
            self.rating_var.set("5")
            self.platform_var.set("PC")
            self.hours_var.set("0")
            self.review_text.delete("1.0", tk.END)
            self.pros_text.delete("1.0", tk.END)
            self.cons_text.delete("1.0", tk.END)

            # Clear game selection if it was set
            if hasattr(self, 'selected_game_id'):
                delattr(self, 'selected_game_id')
            if hasattr(self, 'selected_game_title'):
                delattr(self, 'selected_game_title')

            self.status_label.configure(text="Review submitted successfully")

        except Exception as e:
            messagebox.showerror("Error", f"Error submitting review: {e}")

    def show_search(self):
        """Show professional search view"""
        self.clear_content()
        self.update_nav_selection("Search")

        # Search container
        search_container = tk.Frame(self.content_frame, bg=self.COLORS['primary_bg'])
        search_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # Header
        header_card = self.create_professional_card(search_container, "Advanced Search", 25)
        header_card.pack(fill=tk.X, pady=(0, 20))

        # Search form
        search_frame = tk.Frame(header_card, bg=self.COLORS['card_bg'])
        search_frame.pack(fill=tk.X, padx=25, pady=25)

        # Search entry
        self.advanced_search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame,
                              textvariable=self.advanced_search_var,
                              font=('Segoe UI', 14),
                              bg=self.COLORS['secondary_bg'],
                              fg=self.COLORS['text_primary'],
                              insertbackground=self.COLORS['text_primary'],
                              relief=tk.FLAT,
                              width=50)
        search_entry.pack(side=tk.LEFT, padx=(0, 15))

        search_btn = self.create_professional_button(search_frame, "🔍 Search", self.perform_search)
        search_btn.pack(side=tk.LEFT)

        # Results area
        self.search_results_card = self.create_professional_card(search_container, "Search Results", 25)
        self.search_results_card.pack(fill=tk.BOTH, expand=True)

        # Initial message
        initial_msg = tk.Label(self.search_results_card,
                             text="Enter search terms above to find games, developers, or genres",
                             font=('Segoe UI', 14),
                             bg=self.COLORS['card_bg'],
                             fg=self.COLORS['text_muted'])
        initial_msg.pack(pady=50)

        self.status_label.configure(text="Search ready")

    def perform_search(self):
        """Perform advanced search"""
        query = self.advanced_search_var.get().strip()
        if not query:
            return

        # Clear results area
        for widget in self.search_results_card.winfo_children():
            widget.destroy()

        # Recreate title
        title_label = tk.Label(self.search_results_card,
                             text="Search Results",
                             font=('Segoe UI', 14, 'bold'),
                             bg=self.COLORS['card_bg'],
                             fg=self.COLORS['text_primary'])
        title_label.pack(anchor=tk.W, padx=25, pady=(25, 10))

        # Search database
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT id, title, genre, developer, rating, reviews_count, description
            FROM games
            WHERE title LIKE ? OR genre LIKE ? OR developer LIKE ?
            ORDER BY rating DESC, reviews_count DESC
        ''', (f"%{query}%", f"%{query}%", f"%{query}%"))

        results = cursor.fetchall()

        if not results:
            no_results = tk.Label(self.search_results_card,
                                text=f"No results found for '{query}'",
                                font=('Segoe UI', 12),
                                bg=self.COLORS['card_bg'],
                                fg=self.COLORS['text_muted'])
            no_results.pack(padx=25, pady=20)
            return

        # Results count
        count_label = tk.Label(self.search_results_card,
                             text=f"Found {len(results)} results for '{query}'",
                             font=('Segoe UI', 12),
                             bg=self.COLORS['card_bg'],
                             fg=self.COLORS['text_secondary'])
        count_label.pack(anchor=tk.W, padx=25, pady=(0, 15))

        # Display results
        for game in results:
            result_card = self.create_game_card(self.search_results_card, game)
            result_card.pack(fill=tk.X, padx=25, pady=10)

        self.status_label.configure(text=f"Found {len(results)} results")

    def show_analytics(self):
        """Show professional analytics view"""
        self.clear_content()
        self.update_nav_selection("Analytics")

        # Analytics container
        analytics_container = tk.Frame(self.content_frame, bg=self.COLORS['primary_bg'])
        analytics_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # Header
        header_card = self.create_professional_card(analytics_container, "Analytics & Insights", 25)
        header_card.pack(fill=tk.X, pady=(0, 20))

        # Analytics content
        content_frame = tk.Frame(analytics_container, bg=self.COLORS['primary_bg'])
        content_frame.pack(fill=tk.BOTH, expand=True)

        # Genre analytics
        genre_card = self.create_professional_card(content_frame, "Genre Analysis", 25)
        genre_card.pack(fill=tk.X, pady=(0, 20))

        self.create_genre_analytics(genre_card)

        # Top games
        top_games_card = self.create_professional_card(content_frame, "Top Rated Games", 25)
        top_games_card.pack(fill=tk.BOTH, expand=True)

        self.create_top_games_analytics(top_games_card)

        self.status_label.configure(text="Analytics loaded")

    def create_genre_analytics(self, parent):
        """Create genre analytics section"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT genre, COUNT(*) as count, AVG(rating) as avg_rating
            FROM games
            WHERE genre IS NOT NULL AND reviews_count > 0
            GROUP BY genre
            ORDER BY count DESC
        ''')

        genre_data = cursor.fetchall()

        for genre, count, avg_rating in genre_data:
            genre_frame = tk.Frame(parent, bg=self.COLORS['card_bg'])
            genre_frame.pack(fill=tk.X, padx=25, pady=5)

            # Genre name
            genre_label = tk.Label(genre_frame,
                                 text=f"{genre} ({count} games)",
                                 font=('Segoe UI', 12, 'bold'),
                                 bg=self.COLORS['card_bg'],
                                 fg=self.COLORS['text_primary'])
            genre_label.pack(side=tk.LEFT)

            # Average rating
            rating_label = tk.Label(genre_frame,
                                  text=f"⭐ {avg_rating:.1f}/5",
                                  font=('Segoe UI', 12),
                                  bg=self.COLORS['card_bg'],
                                  fg=self.COLORS['accent_blue'])
            rating_label.pack(side=tk.RIGHT)

    def create_top_games_analytics(self, parent):
        """Create top games analytics section"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT title, rating, reviews_count, genre
            FROM games
            WHERE reviews_count > 0
            ORDER BY rating DESC, reviews_count DESC
            LIMIT 10
        ''')

        top_games = cursor.fetchall()

        for i, (title, rating, reviews_count, genre) in enumerate(top_games, 1):
            game_frame = tk.Frame(parent, bg=self.COLORS['card_bg'])
            game_frame.pack(fill=tk.X, padx=25, pady=8)

            # Rank
            rank_label = tk.Label(game_frame,
                                text=f"#{i}",
                                font=('Segoe UI', 14, 'bold'),
                                bg=self.COLORS['card_bg'],
                                fg=self.COLORS['accent_blue'])
            rank_label.pack(side=tk.LEFT, padx=(0, 15))

            # Game info
            info_frame = tk.Frame(game_frame, bg=self.COLORS['card_bg'])
            info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

            title_label = tk.Label(info_frame,
                                 text=title,
                                 font=('Segoe UI', 12, 'bold'),
                                 bg=self.COLORS['card_bg'],
                                 fg=self.COLORS['text_primary'])
            title_label.pack(anchor=tk.W)

            meta_label = tk.Label(info_frame,
                                text=f"{genre} • {reviews_count} reviews",
                                font=('Segoe UI', 10),
                                bg=self.COLORS['card_bg'],
                                fg=self.COLORS['text_secondary'])
            meta_label.pack(anchor=tk.W)

            # Rating
            rating_label = tk.Label(game_frame,
                                  text=f"⭐ {rating:.1f}/5",
                                  font=('Segoe UI', 12, 'bold'),
                                  bg=self.COLORS['card_bg'],
                                  fg=self.COLORS['accent_blue'])
            rating_label.pack(side=tk.RIGHT)

    def load_sample_data(self):
        """Load professional sample data"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM games")
        if cursor.fetchone()[0] > 0:
            return  # Data already exists

        print("📊 Loading professional sample data...")

        # Professional sample games with detailed information
        games_data = [
            ("God of War", "Action/Adventure", "Santa Monica Studio",
             "Follow Kratos and his son Atreus on their journey through Norse mythology.", 2018, "PC, PlayStation"),
            ("The Witcher 3: Wild Hunt", "RPG", "CD Projekt Red",
             "Hunt monsters and explore a vast open world as Geralt of Rivia.", 2015, "PC, PlayStation, Xbox, Switch"),
            ("Elden Ring", "RPG", "FromSoftware",
             "A fantasy action-RPG adventure set in the Lands Between.", 2022, "PC, PlayStation, Xbox"),
            ("Cyberpunk 2077", "RPG", "CD Projekt Red",
             "An open-world action-adventure story set in Night City.", 2020, "PC, PlayStation, Xbox"),
            ("Hades", "Indie", "Supergiant Games",
             "Defy the god of the dead in this rogue-like dungeon crawler.", 2020, "PC, PlayStation, Xbox, Switch"),
            ("Red Dead Redemption 2", "Action/Adventure", "Rockstar Games",
             "An epic tale of life in America's unforgiving heartland.", 2018, "PC, PlayStation, Xbox"),
            ("Minecraft", "Sandbox", "Mojang Studios",
             "Build, explore, and survive in infinite worlds made of blocks.", 2011, "All Platforms"),
            ("Among Us", "Party", "InnerSloth",
             "Play with 4-15 players as you attempt to prep your spaceship.", 2018, "PC, Mobile, Consoles"),
            ("Valorant", "Shooter", "Riot Games",
             "A 5v5 character-based tactical shooter.", 2020, "PC"),
            ("Stardew Valley", "Simulation", "ConcernedApe",
             "Build the farm of your dreams in this charming countryside RPG.", 2016, "All Platforms"),
            ("Hollow Knight", "Metroidvania", "Team Cherry",
             "An epic action adventure through a vast ruined kingdom.", 2017, "PC, PlayStation, Xbox, Switch"),
            ("FIFA 24", "Sports", "EA Sports",
             "The world's game with HyperMotionV technology.", 2023, "PC, PlayStation, Xbox, Switch")
        ]

        # Insert games
        for title, genre, developer, description, year, platforms in games_data:
            cursor.execute('''
                INSERT INTO games (title, genre, developer, description, release_year, platforms)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (title, genre, developer, description, year, platforms))

        # Professional sample reviews
        reviews_data = [
            (1, "GameMaster Pro", 5, "Absolutely incredible game! The story is captivating and the combat is flawless.",
             '["Amazing story", "Perfect combat", "Beautiful graphics"]', '[]', "PlayStation 5", 45.5),
            (1, "ActionHero", 5, "God of War redefined what action games can be. Masterpiece.",
             '["Emotional depth", "Great gameplay", "Stunning visuals"]', '["None really"]', "PC", 38.2),
            (2, "RPGMaster", 5, "The Witcher 3 is the gold standard for open-world RPGs.",
             '["Massive world", "Great quests", "Amazing characters"]', '["Some bugs"]', "PC", 120.0),
            (2, "FantasyFan", 4, "Incredible game but can be overwhelming at times.",
             '["Rich story", "Beautiful world", "Great side quests"]', '["Complex UI", "Long gameplay"]', "PlayStation 4", 85.3),
            (3, "SoulsVeteran", 5, "FromSoftware's magnum opus. Challenging but fair.",
             '["Perfect difficulty", "Amazing world", "Great bosses"]', '[]', "PC", 95.7),
            (3, "CasualGamer", 3, "Beautiful game but too difficult for casual players.",
             '["Stunning graphics", "Rich lore"]', '["Too hard", "Confusing story"]', "PlayStation 5", 15.2),
            (4, "CyberPunk", 4, "After patches, this game really shines.",
             '["Great story", "Amazing city", "Good graphics"]', '["Still some bugs", "Performance issues"]', "PC", 67.8),
            (5, "IndieExplorer", 5, "Perfect indie game. Addictive and beautifully crafted.",
             '["Perfect gameplay loop", "Great art", "Amazing music"]', '[]', "Nintendo Switch", 42.1),
            (6, "WesternFan", 5, "The most immersive open world ever created.",
             '["Incredible detail", "Amazing story", "Perfect atmosphere"]', '["Slow start"]', "PlayStation 4", 78.9),
            (7, "Builder", 5, "Endless creativity and fun. Perfect for all ages.",
             '["Infinite possibilities", "Great multiplayer", "Regular updates"]', '[]', "PC", 200.0),
            (8, "Detective", 4, "Simple but incredibly fun with friends.",
             '["Great social game", "Easy to learn", "Fun with friends"]', '["Gets repetitive", "Limited content"]', "Mobile", 25.3),
            (9, "TacticalShooter", 4, "Excellent competitive shooter with unique mechanics.",
             '["Great gunplay", "Unique abilities", "Good balance"]', '["Steep learning curve", "Toxic community"]', "PC", 156.7),
            (10, "FarmingFan", 5, "The most relaxing and addictive game ever made.",
             '["Incredibly relaxing", "Addictive gameplay", "Charming art"]', '[]', "PC", 89.4),
            (11, "MetroidvaniaLover", 5, "Perfect example of the genre. Atmospheric masterpiece.",
             '["Amazing atmosphere", "Perfect controls", "Beautiful art"]', '["Can be confusing"]', "Nintendo Switch", 34.6),
            (12, "SoccerFan", 3, "Decent football game but lacks innovation.",
             '["Good graphics", "Realistic gameplay"]', '["Same as last year", "Expensive", "Pay-to-win"]', "PlayStation 5", 28.1)
        ]

        # Insert reviews
        for game_id, reviewer, rating, text, pros, cons, platform, hours in reviews_data:
            cursor.execute('''
                INSERT INTO reviews (game_id, reviewer, rating, review_text, pros, cons, platform, hours_played, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (game_id, reviewer, rating, text, pros, cons, platform, hours, datetime.now().strftime("%Y-%m-%d")))

        # Update game statistics
        cursor.execute('''
            UPDATE games SET
                rating = (SELECT AVG(rating) FROM reviews WHERE reviews.game_id = games.id),
                reviews_count = (SELECT COUNT(*) FROM reviews WHERE reviews.game_id = games.id)
        ''')

        self.conn.commit()
        print("✅ Professional sample data loaded")

    def run(self):
        """Start the professional application"""
        print("🚀 Starting GameReview Pro - Professional Edition...")
        self.root.mainloop()
        print("👋 Professional application closed")

def main():
    """Main entry point for professional application"""
    try:
        print("🎮 GameReview Pro - Professional Edition")
        print("🎨 Swarm 2015 Inspired Design")
        print("=" * 60)

        app = ProfessionalGameReviewApp()
        app.run()

    except Exception as e:
        print(f"❌ Error: {e}")
        messagebox.showerror("Application Error", f"An error occurred: {e}")

if __name__ == "__main__":
    main()

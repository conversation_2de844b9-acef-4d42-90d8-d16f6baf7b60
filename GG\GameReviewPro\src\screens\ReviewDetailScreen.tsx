// Professional Review Detail Screen
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  Share,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import FastImage from 'react-native-fast-image';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Toast from 'react-native-toast-message';
import { Review, NavigationProps } from '../types';
import { colors, typography, layout, shadows } from '../theme';
import { apiService } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

export const ReviewDetailScreen: React.FC<NavigationProps> = ({ navigation, route }) => {
  const { reviewId } = route.params;
  const [review, setReview] = useState<Review | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadReview();
  }, [reviewId]);

  const loadReview = async () => {
    try {
      setError(null);
      const response = await apiService.getReview(reviewId);
      
      if (response.success && response.data) {
        setReview(response.data);
      } else {
        setError('Review not found');
      }
    } catch (error) {
      console.error('Error loading review:', error);
      setError('Failed to load review');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadReview();
  };

  const likeReview = async () => {
    if (!review) return;

    try {
      const response = await apiService.likeReview(review.id);
      if (response.success) {
        setReview({
          ...review,
          likes_count: response.data?.likes_count || review.likes_count + 1
        });
        
        Toast.show({
          type: 'success',
          text1: 'Review Liked!',
          text2: 'Thank you for your feedback',
        });
      }
    } catch (error) {
      console.error('Error liking review:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to like review',
      });
    }
  };

  const shareReview = async () => {
    if (!review) return;

    try {
      const message = `Check out this review of ${review.game_title}:\n\n"${review.review_text}"\n\nRating: ${review.rating}/5 stars\n\nShared from GameReview Pro`;
      
      await Share.share({
        message,
        title: `Review: ${review.game_title}`,
      });
    } catch (error) {
      console.error('Error sharing review:', error);
    }
  };

  const navigateToGame = () => {
    if (review?.game_id) {
      navigation.navigate('GameDetail', { gameId: review.game_id });
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Icon
          key={i}
          name={i <= rating ? 'star' : 'star-border'}
          size={24}
          color={colors.gold}
        />
      );
    }
    return stars;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getPlatformColor = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'pc': return colors.amber;
      case 'playstation 4':
      case 'playstation 5': return '#003087';
      case 'xbox one':
      case 'xbox series x/s': return '#107C10';
      case 'nintendo switch': return '#E60012';
      case 'ios':
      case 'android': return colors.green;
      default: return colors.primaryBlue;
    }
  };

  if (loading) {
    return <LoadingSpinner message="Loading review..." />;
  }

  if (error || !review) {
    return <ErrorMessage message={error || 'Review not found'} onRetry={loadReview} />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primaryBlue}
            colors={[colors.primaryBlue]}
          />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.reviewerInfo}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>
                {review.reviewer_name.charAt(0).toUpperCase()}
              </Text>
            </View>
            <View style={styles.reviewerDetails}>
              <Text style={styles.reviewerName}>{review.reviewer_name}</Text>
              <Text style={styles.reviewDate}>{formatDate(review.created_at)}</Text>
            </View>
          </View>

          <View style={styles.headerActions}>
            <TouchableOpacity onPress={shareReview} style={styles.actionButton}>
              <Icon name="share" size={24} color={colors.primaryBlue} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Game Info */}
        {review.game_title && (
          <TouchableOpacity style={styles.gameSection} onPress={navigateToGame}>
            <FastImage
              source={{
                uri: review.game_cover || 'https://via.placeholder.com/80x100/1A1A1A/FFFFFF?text=Game',
                priority: FastImage.priority.normal,
              }}
              style={styles.gameImage}
              resizeMode={FastImage.resizeMode.cover}
            />
            <View style={styles.gameInfo}>
              <Text style={styles.gameTitle}>{review.game_title}</Text>
              <Text style={styles.viewGameText}>View Game Details</Text>
            </View>
            <Icon name="chevron-right" size={24} color={colors.lightGray} />
          </TouchableOpacity>
        )}

        {/* Rating */}
        <View style={styles.ratingSection}>
          <Text style={styles.sectionTitle}>Rating</Text>
          <View style={styles.ratingContainer}>
            <View style={styles.stars}>
              {renderStars(review.rating)}
            </View>
            <Text style={styles.ratingText}>{review.rating}/5 stars</Text>
          </View>
        </View>

        {/* Platform */}
        {review.platform && (
          <View style={styles.platformSection}>
            <Text style={styles.sectionTitle}>Platform</Text>
            <View style={[styles.platformBadge, { backgroundColor: getPlatformColor(review.platform) }]}>
              <Text style={styles.platformText}>{review.platform}</Text>
            </View>
          </View>
        )}

        {/* Review Text */}
        {review.review_text && (
          <View style={styles.reviewSection}>
            <Text style={styles.sectionTitle}>Review</Text>
            <Text style={styles.reviewText}>{review.review_text}</Text>
          </View>
        )}

        {/* Pros and Cons */}
        {(review.pros.length > 0 || review.cons.length > 0) && (
          <View style={styles.prosConsSection}>
            {review.pros.length > 0 && (
              <View style={styles.prosContainer}>
                <View style={styles.prosConsHeader}>
                  <Icon name="thumb-up" size={20} color={colors.green} />
                  <Text style={styles.prosConsTitle}>Pros</Text>
                </View>
                {review.pros.map((pro, index) => (
                  <Text key={index} style={styles.prosConsItem}>• {pro}</Text>
                ))}
              </View>
            )}

            {review.cons.length > 0 && (
              <View style={styles.consContainer}>
                <View style={styles.prosConsHeader}>
                  <Icon name="thumb-down" size={20} color={colors.red} />
                  <Text style={styles.prosConsTitle}>Cons</Text>
                </View>
                {review.cons.map((con, index) => (
                  <Text key={index} style={styles.prosConsItem}>• {con}</Text>
                ))}
              </View>
            )}
          </View>
        )}

        {/* Review Image */}
        {review.image_path && (
          <View style={styles.imageSection}>
            <Text style={styles.sectionTitle}>Screenshot</Text>
            <FastImage
              source={{ uri: review.image_path }}
              style={styles.reviewImage}
              resizeMode={FastImage.resizeMode.cover}
            />
          </View>
        )}

        {/* Stats */}
        <View style={styles.statsSection}>
          <View style={styles.statItem}>
            <Icon name="visibility" size={20} color={colors.lightGray} />
            <Text style={styles.statText}>{review.views_count} views</Text>
          </View>
          <View style={styles.statItem}>
            <Icon name="thumb-up" size={20} color={colors.primaryBlue} />
            <Text style={styles.statText}>{review.likes_count} likes</Text>
          </View>
        </View>

        {/* Action Button */}
        <TouchableOpacity style={styles.likeButton} onPress={likeReview}>
          <Icon name="thumb-up" size={24} color={colors.white} />
          <Text style={styles.likeButtonText}>Like This Review</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: layout.screenPadding,
    borderBottomWidth: 1,
    borderBottomColor: colors.mediumGray,
  },
  reviewerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primaryBlue,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: layout.md,
  },
  avatarText: {
    ...typography.h3,
    color: colors.white,
    fontWeight: '600',
  },
  reviewerDetails: {
    flex: 1,
  },
  reviewerName: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
  reviewDate: {
    ...typography.caption,
    color: colors.lightGray,
    marginTop: layout.xs / 2,
  },
  headerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.darkGray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  gameSection: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: layout.screenPadding,
    backgroundColor: colors.darkGray,
    marginHorizontal: layout.screenPadding,
    marginTop: layout.md,
    borderRadius: layout.borderRadius,
    ...shadows.small,
  },
  gameImage: {
    width: 60,
    height: 80,
    borderRadius: layout.borderRadius / 2,
    marginRight: layout.md,
  },
  gameInfo: {
    flex: 1,
  },
  gameTitle: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
  viewGameText: {
    ...typography.caption,
    color: colors.primaryBlue,
    marginTop: layout.xs / 2,
  },
  ratingSection: {
    padding: layout.screenPadding,
  },
  sectionTitle: {
    ...typography.h3,
    color: colors.white,
    marginBottom: layout.sm,
  },
  ratingContainer: {
    alignItems: 'center',
  },
  stars: {
    flexDirection: 'row',
    marginBottom: layout.sm,
  },
  ratingText: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
  platformSection: {
    paddingHorizontal: layout.screenPadding,
    marginBottom: layout.md,
  },
  platformBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: layout.md,
    paddingVertical: layout.sm,
    borderRadius: layout.borderRadius,
  },
  platformText: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
  reviewSection: {
    padding: layout.screenPadding,
  },
  reviewText: {
    ...typography.body,
    color: colors.lightGray,
    lineHeight: 24,
  },
  prosConsSection: {
    paddingHorizontal: layout.screenPadding,
    marginBottom: layout.md,
  },
  prosContainer: {
    marginBottom: layout.md,
  },
  consContainer: {
    marginBottom: layout.md,
  },
  prosConsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: layout.sm,
  },
  prosConsTitle: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
    marginLeft: layout.sm,
  },
  prosConsItem: {
    ...typography.body,
    color: colors.lightGray,
    marginBottom: layout.xs / 2,
    marginLeft: layout.lg,
  },
  imageSection: {
    padding: layout.screenPadding,
  },
  reviewImage: {
    width: '100%',
    height: 250,
    borderRadius: layout.borderRadius,
  },
  statsSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: layout.screenPadding,
    marginBottom: layout.lg,
    gap: layout.xl,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    ...typography.body,
    color: colors.lightGray,
    marginLeft: layout.sm,
  },
  likeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primaryBlue,
    marginHorizontal: layout.screenPadding,
    marginBottom: layout.xl,
    paddingVertical: layout.md,
    borderRadius: layout.borderRadius,
    gap: layout.sm,
    ...shadows.medium,
  },
  likeButtonText: {
    ...typography.button,
    color: colors.white,
  },
});

export default ReviewDetailScreen;

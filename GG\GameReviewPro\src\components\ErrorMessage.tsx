// Professional Error Message Component
import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, layout, shadows } from '../theme';

interface ErrorMessageProps {
  message: string;
  onRetry?: () => void;
  style?: any;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({ 
  message, 
  onRetry,
  style 
}) => {
  return (
    <View style={[styles.container, style]}>
      <View style={styles.errorCard}>
        <Icon name="error-outline" size={48} color={colors.red} style={styles.icon} />
        <Text style={styles.title}>Oops! Something went wrong</Text>
        <Text style={styles.message}>{message}</Text>
        
        {onRetry && (
          <TouchableOpacity
            style={styles.retryButton}
            onPress={onRetry}
            activeOpacity={0.8}
          >
            <Icon name="refresh" size={20} color={colors.white} />
            <Text style={styles.retryText}>Try Again</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.black,
    padding: layout.lg,
  },
  errorCard: {
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    padding: layout.xl,
    alignItems: 'center',
    maxWidth: 300,
    ...shadows.medium,
  },
  icon: {
    marginBottom: layout.md,
  },
  title: {
    ...typography.h3,
    color: colors.white,
    textAlign: 'center',
    marginBottom: layout.sm,
  },
  message: {
    ...typography.body,
    color: colors.lightGray,
    textAlign: 'center',
    marginBottom: layout.lg,
    lineHeight: 22,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primaryBlue,
    paddingHorizontal: layout.lg,
    paddingVertical: layout.sm,
    borderRadius: layout.borderRadius,
    gap: layout.sm,
  },
  retryText: {
    ...typography.button,
    color: colors.white,
  },
});

export default ErrorMessage;

"""
Validation utilities for GameReview Application
"""

from typing import Dict, List, Tuple, Any

def validate_rating(rating: Any) -> <PERSON>ple[bool, str]:
    """Validate rating value"""
    try:
        rating_int = int(rating)
        if 1 <= rating_int <= 5:
            return True, ""
        else:
            return False, "Rating must be between 1 and 5"
    except (ValueError, TypeError):
        return False, "Rating must be a valid integer"

def validate_review_data(data: Dict) -> <PERSON><PERSON>[bool, List[str]]:
    """Validate review creation data"""
    errors = []
    
    # Required fields
    if not data.get('game_id'):
        errors.append("Game ID is required")
    
    if not data.get('rating'):
        errors.append("Rating is required")
    else:
        is_valid, error_msg = validate_rating(data['rating'])
        if not is_valid:
            errors.append(error_msg)
    
    # Optional field validation
    reviewer_name = data.get('reviewer_name', '').strip()
    if reviewer_name and len(reviewer_name) > 100:
        errors.append("Reviewer name must be less than 100 characters")
    
    review_text = data.get('review_text', '').strip()
    if review_text and len(review_text) > 2000:
        errors.append("Review text must be less than 2000 characters")
    
    platform = data.get('platform', '').strip()
    if platform and len(platform) > 50:
        errors.append("Platform name must be less than 50 characters")
    
    # Validate pros and cons
    pros = data.get('pros', [])
    if pros and isinstance(pros, list):
        for i, pro in enumerate(pros):
            if isinstance(pro, str) and len(pro) > 200:
                errors.append(f"Pro #{i+1} must be less than 200 characters")
    
    cons = data.get('cons', [])
    if cons and isinstance(cons, list):
        for i, con in enumerate(cons):
            if isinstance(con, str) and len(con) > 200:
                errors.append(f"Con #{i+1} must be less than 200 characters")
    
    return len(errors) == 0, errors

def validate_game_data(data: Dict) -> Tuple[bool, List[str]]:
    """Validate game creation/update data"""
    errors = []
    
    # Required fields
    if not data.get('title', '').strip():
        errors.append("Game title is required")
    elif len(data['title'].strip()) > 200:
        errors.append("Game title must be less than 200 characters")
    
    # Optional field validation
    description = data.get('description', '').strip()
    if description and len(description) > 1000:
        errors.append("Description must be less than 1000 characters")
    
    genre = data.get('genre', '').strip()
    if genre and len(genre) > 50:
        errors.append("Genre must be less than 50 characters")
    
    return len(errors) == 0, errors

def validate_pagination_params(page: Any, limit: Any, max_limit: int = 100) -> Tuple[int, int, List[str]]:
    """Validate and normalize pagination parameters"""
    errors = []
    
    # Validate page
    try:
        page_int = int(page) if page else 1
        if page_int < 1:
            page_int = 1
            errors.append("Page must be greater than 0, defaulting to 1")
    except (ValueError, TypeError):
        page_int = 1
        errors.append("Invalid page parameter, defaulting to 1")
    
    # Validate limit
    try:
        limit_int = int(limit) if limit else 20
        if limit_int < 1:
            limit_int = 20
            errors.append("Limit must be greater than 0, defaulting to 20")
        elif limit_int > max_limit:
            limit_int = max_limit
            errors.append(f"Limit cannot exceed {max_limit}, setting to {max_limit}")
    except (ValueError, TypeError):
        limit_int = 20
        errors.append("Invalid limit parameter, defaulting to 20")
    
    return page_int, limit_int, errors

def sanitize_string(value: Any, max_length: int = None) -> str:
    """Sanitize string input"""
    if not value:
        return ""
    
    # Convert to string and strip whitespace
    sanitized = str(value).strip()
    
    # Truncate if max_length specified
    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized

def validate_search_query(query: Any) -> Tuple[bool, str, str]:
    """Validate search query"""
    if not query:
        return False, "", "Search query is required"
    
    query_str = sanitize_string(query, 100)
    
    if len(query_str) < 2:
        return False, query_str, "Search query must be at least 2 characters"
    
    return True, query_str, ""

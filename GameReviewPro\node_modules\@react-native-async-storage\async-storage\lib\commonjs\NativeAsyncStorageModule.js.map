{"version": 3, "names": ["_reactNative", "require", "_default", "TurboModuleRegistry", "get", "exports", "default"], "sources": ["NativeAsyncStorageModule.ts"], "sourcesContent": ["import type { TurboModule } from \"react-native\";\nimport { TurboModuleRegistry } from \"react-native\";\nimport type { Error<PERSON>ike } from \"./types\";\n\nexport interface Spec extends TurboModule {\n  multiGet: (\n    keys: string[],\n    callback: (error?: ErrorLike[], result?: [string, string][]) => void\n  ) => void;\n  multiSet: (\n    kvPairs: [string, string][],\n    callback: (error?: ErrorLike[]) => void\n  ) => void;\n  multiRemove: (\n    keys: readonly string[],\n    callback: (error?: ErrorLike[]) => void\n  ) => void;\n  multiMerge: (\n    kvPairs: [string, string][],\n    callback: (error?: ErrorLike[]) => void\n  ) => void;\n  getAllKeys: (\n    callback: (error?: ErrorLike[], result?: [string, string][]) => void\n  ) => void;\n  clear: (callback: (error?: ErrorLike[]) => void) => void;\n}\n\nexport default TurboModuleRegistry.get<Spec>(\"RNCAsyncStorage\");\n"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAAmD,IAAAC,QAAA,GA0BpCC,gCAAmB,CAACC,GAAG,CAAO,iBAAiB,CAAC;AAAAC,OAAA,CAAAC,OAAA,GAAAJ,QAAA"}
"""
Configuration settings for GameReview Flask Application
"""

import os
from datetime import timedelta

class Config:
    """Base configuration class"""
    
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'gamereviews-professional-app-2024'
    
    # Database settings
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'database.db'
    
    # File upload settings
    UPLOAD_FOLDER = 'static/uploads'
    STATIC_FOLDER = 'static'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    
    # Allowed file extensions
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    
    # API settings
    API_TITLE = 'GameReview Pro API'
    API_VERSION = '1.0.0'
    API_DESCRIPTION = 'Professional game review platform API'
    
    # Pagination settings
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100
    
    # Image processing settings
    MAX_IMAGE_WIDTH = 1200
    IMAGE_QUALITY = 85
    
    # CORS settings
    CORS_ORIGINS = ['*']  # In production, specify exact origins
    
    @staticmethod
    def init_app(app):
        """Initialize app with configuration"""
        pass

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Production database (PostgreSQL recommended)
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'postgresql://user:pass@localhost/gamereviews'
    
    # Production file storage (AWS S3 recommended)
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'static/uploads'
    
    # Security settings
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    DATABASE_URL = ':memory:'  # In-memory SQLite for tests
    WTF_CSRF_ENABLED = False

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

"""
Database models for GameReview Pro Python Application
Using SQLAlchemy for modern Python ORM
"""

from sqlalchemy import create_engine, Column, Integer, String, Float, Text, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime
import json

Base = declarative_base()

class Game(Base):
    """Game model with enhanced features"""
    __tablename__ = 'games'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text)
    genre = Column(String(100), index=True)
    platforms = Column(Text)  # JSON string
    developer = Column(String(200))
    publisher = Column(String(200))
    release_date = Column(String(50))
    cover_image = Column(String(500))
    trailer_url = Column(String(500))
    official_website = Column(String(500))
    metacritic_score = Column(Integer)
    steam_id = Column(String(50))
    price = Column(Float)
    
    # Calculated fields
    average_rating = Column(Float, default=0.0)
    total_reviews = Column(Integer, default=0)
    sentiment_score = Column(Float, default=0.0)  # AI sentiment analysis
    popularity_score = Column(Float, default=0.0)  # AI popularity ranking
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    reviews = relationship("Review", back_populates="game", cascade="all, delete-orphan")
    
    def to_dict(self):
        """Convert to dictionary"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'genre': self.genre,
            'platforms': json.loads(self.platforms) if self.platforms else [],
            'developer': self.developer,
            'publisher': self.publisher,
            'release_date': self.release_date,
            'cover_image': self.cover_image,
            'trailer_url': self.trailer_url,
            'official_website': self.official_website,
            'metacritic_score': self.metacritic_score,
            'steam_id': self.steam_id,
            'price': self.price,
            'average_rating': self.average_rating,
            'total_reviews': self.total_reviews,
            'sentiment_score': self.sentiment_score,
            'popularity_score': self.popularity_score,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Review(Base):
    """Review model with AI analysis"""
    __tablename__ = 'reviews'
    
    id = Column(Integer, primary_key=True)
    game_id = Column(Integer, ForeignKey('games.id'), nullable=False, index=True)
    reviewer_name = Column(String(100), default='Anonymous')
    rating = Column(Integer, nullable=False)  # 1-5 stars
    review_text = Column(Text)
    platform = Column(String(100))
    hours_played = Column(Float)
    pros = Column(Text)  # JSON string
    cons = Column(Text)  # JSON string
    
    # AI Analysis fields
    sentiment_score = Column(Float, default=0.0)  # -1 to 1
    emotion_scores = Column(Text)  # JSON: joy, anger, sadness, etc.
    readability_score = Column(Float, default=0.0)
    helpfulness_score = Column(Float, default=0.0)
    toxicity_score = Column(Float, default=0.0)
    
    # Social features
    likes_count = Column(Integer, default=0)
    dislikes_count = Column(Integer, default=0)
    views_count = Column(Integer, default=0)
    helpful_votes = Column(Integer, default=0)
    
    # Media
    image_path = Column(String(500))
    video_path = Column(String(500))
    
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    game = relationship("Game", back_populates="reviews")
    
    def to_dict(self):
        """Convert to dictionary"""
        return {
            'id': self.id,
            'game_id': self.game_id,
            'reviewer_name': self.reviewer_name,
            'rating': self.rating,
            'review_text': self.review_text,
            'platform': self.platform,
            'hours_played': self.hours_played,
            'pros': json.loads(self.pros) if self.pros else [],
            'cons': json.loads(self.cons) if self.cons else [],
            'sentiment_score': self.sentiment_score,
            'emotion_scores': json.loads(self.emotion_scores) if self.emotion_scores else {},
            'readability_score': self.readability_score,
            'helpfulness_score': self.helpfulness_score,
            'toxicity_score': self.toxicity_score,
            'likes_count': self.likes_count,
            'dislikes_count': self.dislikes_count,
            'views_count': self.views_count,
            'helpful_votes': self.helpful_votes,
            'image_path': self.image_path,
            'video_path': self.video_path,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class User(Base):
    """User model for personalization"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(100), unique=True, nullable=False)
    email = Column(String(200), unique=True)
    avatar_path = Column(String(500))
    
    # Preferences
    favorite_genres = Column(Text)  # JSON array
    favorite_platforms = Column(Text)  # JSON array
    preferred_rating_style = Column(String(50))  # detailed, quick, etc.
    
    # AI Profile
    gaming_personality = Column(Text)  # JSON: casual, hardcore, etc.
    recommendation_weights = Column(Text)  # JSON: genre weights, etc.
    
    # Stats
    total_reviews = Column(Integer, default=0)
    total_games_played = Column(Integer, default=0)
    average_rating_given = Column(Float, default=0.0)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    last_active = Column(DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        """Convert to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'avatar_path': self.avatar_path,
            'favorite_genres': json.loads(self.favorite_genres) if self.favorite_genres else [],
            'favorite_platforms': json.loads(self.favorite_platforms) if self.favorite_platforms else [],
            'preferred_rating_style': self.preferred_rating_style,
            'gaming_personality': json.loads(self.gaming_personality) if self.gaming_personality else {},
            'recommendation_weights': json.loads(self.recommendation_weights) if self.recommendation_weights else {},
            'total_reviews': self.total_reviews,
            'total_games_played': self.total_games_played,
            'average_rating_given': self.average_rating_given,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_active': self.last_active.isoformat() if self.last_active else None
        }

# Database setup
class DatabaseManager:
    """Database manager with SQLAlchemy"""
    
    def __init__(self, db_path="gamereviews.db"):
        self.engine = create_engine(f'sqlite:///{db_path}', echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        self.create_tables()
    
    def create_tables(self):
        """Create all tables"""
        Base.metadata.create_all(bind=self.engine)
        print("✅ Database tables created successfully!")
    
    def get_session(self):
        """Get database session"""
        return self.SessionLocal()
    
    def close(self):
        """Close database connection"""
        self.engine.dispose()

# Global database instance
db_manager = DatabaseManager()

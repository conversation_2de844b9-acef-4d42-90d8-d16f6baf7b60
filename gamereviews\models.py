"""
Database Models for GameReview Mobile App
Simple SQLite-based models without ORM for better performance
"""

import sqlite3
from datetime import datetime
from typing import List, Dict, Optional

class Game:
    """Game model for managing game data"""
    
    def __init__(self, id=None, title=None, description=None, genre=None, 
                 platforms=None, cover_image=None, average_rating=0.0, 
                 total_reviews=0, created_at=None):
        self.id = id
        self.title = title
        self.description = description
        self.genre = genre
        self.platforms = platforms  # JSON string of platforms
        self.cover_image = cover_image
        self.average_rating = average_rating
        self.total_reviews = total_reviews
        self.created_at = created_at or datetime.now()
    
    def to_dict(self) -> Dict:
        """Convert game object to dictionary"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'genre': self.genre,
            'platforms': self.platforms.split(',') if self.platforms else [],
            'cover_image': self.cover_image,
            'average_rating': self.average_rating,
            'total_reviews': self.total_reviews,
            'created_at': self.created_at.isoformat() if isinstance(self.created_at, datetime) else self.created_at
        }
    
    @staticmethod
    def from_row(row) -> 'Game':
        """Create Game object from database row"""
        return Game(
            id=row['id'],
            title=row['title'],
            description=row['description'],
            genre=row['genre'],
            platforms=row['platforms'],
            cover_image=row['cover_image'],
            average_rating=row['average_rating'],
            total_reviews=row['total_reviews'],
            created_at=row['created_at']
        )

class Review:
    """Review model for managing review data"""
    
    def __init__(self, id=None, game_id=None, reviewer_name='Anonymous', 
                 rating=None, review_text=None, platform=None, pros=None, 
                 cons=None, image_path=None, likes_count=0, views_count=0, 
                 created_at=None):
        self.id = id
        self.game_id = game_id
        self.reviewer_name = reviewer_name or 'Anonymous'
        self.rating = rating
        self.review_text = review_text
        self.platform = platform
        self.pros = pros
        self.cons = cons
        self.image_path = image_path
        self.likes_count = likes_count
        self.views_count = views_count
        self.created_at = created_at or datetime.now()
    
    def to_dict(self) -> Dict:
        """Convert review object to dictionary"""
        return {
            'id': self.id,
            'game_id': self.game_id,
            'reviewer_name': self.reviewer_name,
            'rating': self.rating,
            'review_text': self.review_text,
            'platform': self.platform,
            'pros': self.pros.split('|') if self.pros else [],
            'cons': self.cons.split('|') if self.cons else [],
            'image_path': self.image_path,
            'likes_count': self.likes_count,
            'views_count': self.views_count,
            'created_at': self.created_at.isoformat() if isinstance(self.created_at, datetime) else self.created_at
        }
    
    @staticmethod
    def from_row(row) -> 'Review':
        """Create Review object from database row"""
        return Review(
            id=row['id'],
            game_id=row['game_id'],
            reviewer_name=row['reviewer_name'],
            rating=row['rating'],
            review_text=row['review_text'],
            platform=row['platform'],
            pros=row['pros'],
            cons=row['cons'],
            image_path=row['image_path'],
            likes_count=row['likes_count'],
            views_count=row['views_count'],
            created_at=row['created_at']
        )

class DatabaseManager:
    """Database operations manager"""
    
    @staticmethod
    def get_connection():
        """Get database connection"""
        conn = sqlite3.connect('database.db')
        conn.row_factory = sqlite3.Row
        return conn
    
    @staticmethod
    def execute_query(query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """Execute SELECT query and return results"""
        conn = DatabaseManager.get_connection()
        try:
            cursor = conn.execute(query, params)
            results = cursor.fetchall()
            return results
        finally:
            conn.close()
    
    @staticmethod
    def execute_update(query: str, params: tuple = ()) -> int:
        """Execute INSERT/UPDATE/DELETE query and return affected rows"""
        conn = DatabaseManager.get_connection()
        try:
            cursor = conn.execute(query, params)
            conn.commit()
            return cursor.rowcount
        finally:
            conn.close()
    
    @staticmethod
    def execute_insert(query: str, params: tuple = ()) -> int:
        """Execute INSERT query and return last row ID"""
        conn = DatabaseManager.get_connection()
        try:
            cursor = conn.execute(query, params)
            conn.commit()
            return cursor.lastrowid
        finally:
            conn.close()

# Platform constants
GAMING_PLATFORMS = [
    'PC',
    'PlayStation 5',
    'PlayStation 4',
    'Xbox Series X/S',
    'Xbox One',
    'Nintendo Switch',
    'iOS',
    'Android',
    'Steam Deck',
    'Epic Games Store',
    'Steam'
]

# Genre constants
GAME_GENRES = [
    'Action',
    'Adventure',
    'RPG',
    'Strategy',
    'Sports',
    'Racing',
    'Simulation',
    'Puzzle',
    'Fighting',
    'Shooter',
    'Horror',
    'Platformer',
    'MMORPG',
    'Battle Royale',
    'Indie'
]

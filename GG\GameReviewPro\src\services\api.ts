// Professional API Service
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  Game, 
  Review, 
  CreateReviewRequest, 
  GamesResponse, 
  ReviewsResponse, 
  ApiResponse,
  AppStats 
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    // Use your Flask backend URL
    this.baseURL = 'http://localhost:5000/api';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Health Check
  async healthCheck(): Promise<ApiResponse<any>> {
    try {
      const response = await this.api.get('/health');
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: this.getErrorMessage(error) };
    }
  }

  // Games API
  async getGames(params?: {
    page?: number;
    limit?: number;
    genre?: string;
    platform?: string;
    sort?: string;
  }): Promise<GamesResponse> {
    try {
      const response = await this.api.get('/games', { params });
      return response.data;
    } catch (error) {
      throw new Error(this.getErrorMessage(error));
    }
  }

  async getGame(gameId: number): Promise<ApiResponse<Game>> {
    try {
      const response = await this.api.get(`/games/${gameId}`);
      return response.data;
    } catch (error) {
      return { success: false, error: this.getErrorMessage(error) };
    }
  }

  async searchGames(query: string): Promise<ApiResponse<Game[]>> {
    try {
      const response = await this.api.get('/games/search', { 
        params: { q: query } 
      });
      return response.data;
    } catch (error) {
      return { success: false, error: this.getErrorMessage(error) };
    }
  }

  async getTopRatedGames(limit: number = 10): Promise<ApiResponse<Game[]>> {
    try {
      const response = await this.api.get('/games/top-rated', { 
        params: { limit } 
      });
      return response.data;
    } catch (error) {
      return { success: false, error: this.getErrorMessage(error) };
    }
  }

  // Reviews API
  async getReviews(params?: {
    page?: number;
    limit?: number;
    game_id?: number;
  }): Promise<ReviewsResponse> {
    try {
      const response = await this.api.get('/reviews', { params });
      return response.data;
    } catch (error) {
      throw new Error(this.getErrorMessage(error));
    }
  }

  async getReview(reviewId: number): Promise<ApiResponse<Review>> {
    try {
      const response = await this.api.get(`/reviews/${reviewId}`);
      return response.data;
    } catch (error) {
      return { success: false, error: this.getErrorMessage(error) };
    }
  }

  async createReview(reviewData: CreateReviewRequest): Promise<ApiResponse<any>> {
    try {
      const response = await this.api.post('/reviews', reviewData);
      return response.data;
    } catch (error) {
      return { success: false, error: this.getErrorMessage(error) };
    }
  }

  async likeReview(reviewId: number): Promise<ApiResponse<any>> {
    try {
      const response = await this.api.put(`/reviews/${reviewId}/like`);
      return response.data;
    } catch (error) {
      return { success: false, error: this.getErrorMessage(error) };
    }
  }

  async getReviewsForGame(gameId: number, params?: {
    page?: number;
    limit?: number;
    sort?: string;
  }): Promise<ReviewsResponse> {
    try {
      const response = await this.api.get(`/reviews/game/${gameId}`, { params });
      return response.data;
    } catch (error) {
      throw new Error(this.getErrorMessage(error));
    }
  }

  // Image Upload
  async uploadReviewImage(imageUri: string): Promise<ApiResponse<{ image_url: string }>> {
    try {
      const formData = new FormData();
      formData.append('image', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'review-image.jpg',
      } as any);

      const response = await this.api.post('/upload/review-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      return { success: false, error: this.getErrorMessage(error) };
    }
  }

  // Utility APIs
  async getPlatforms(): Promise<ApiResponse<string[]>> {
    try {
      const response = await this.api.get('/platforms');
      return response.data;
    } catch (error) {
      return { success: false, error: this.getErrorMessage(error) };
    }
  }

  async getGenres(): Promise<ApiResponse<string[]>> {
    try {
      const response = await this.api.get('/genres');
      return response.data;
    } catch (error) {
      return { success: false, error: this.getErrorMessage(error) };
    }
  }

  async getStats(): Promise<ApiResponse<AppStats>> {
    try {
      const response = await this.api.get('/stats');
      return response.data;
    } catch (error) {
      return { success: false, error: this.getErrorMessage(error) };
    }
  }

  // Helper method to extract error messages
  private getErrorMessage(error: any): string {
    if (error.response?.data?.error) {
      return error.response.data.error;
    }
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    if (error.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  }

  // Get full image URL
  getImageUrl(imagePath: string): string {
    if (!imagePath) return '';
    if (imagePath.startsWith('http')) return imagePath;
    return `${this.baseURL.replace('/api', '')}${imagePath}`;
  }
}

export const apiService = new ApiService();
export default apiService;

// Professional Profile Screen
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import { NavigationProps, AppStats } from '../types';
import { colors, typography, layout, shadows } from '../theme';
import { apiService } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';

export const ProfileScreen: React.FC<NavigationProps> = ({ navigation }) => {
  const [stats, setStats] = useState<AppStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const response = await apiService.getStats();
      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const openLink = (url: string) => {
    Linking.openURL(url);
  };

  const menuItems = [
    {
      icon: 'rate-review',
      title: 'Write a Review',
      subtitle: 'Share your gaming experience',
      onPress: () => navigation.navigate('WriteReview'),
      color: colors.primaryBlue,
    },
    {
      icon: 'explore',
      title: 'Discover Games',
      subtitle: 'Find your next favorite game',
      onPress: () => navigation.navigate('Discover'),
      color: colors.green,
    },
    {
      icon: 'info',
      title: 'About GameReview Pro',
      subtitle: 'Learn more about our app',
      onPress: () => {},
      color: colors.amber,
    },
    {
      icon: 'help',
      title: 'Help & Support',
      subtitle: 'Get help and contact us',
      onPress: () => {},
      color: colors.cyan,
    },
  ];

  const socialLinks = [
    {
      icon: 'language',
      title: 'Website',
      url: 'https://gamereviewpro.com',
    },
    {
      icon: 'email',
      title: 'Contact Us',
      url: 'mailto:<EMAIL>',
    },
  ];

  if (loading) {
    return <LoadingSpinner message="Loading profile..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <LinearGradient
          colors={[colors.primaryBlue, colors.cyan]}
          style={styles.header}
        >
          <View style={styles.profileInfo}>
            <View style={styles.avatar}>
              <Icon name="person" size={48} color={colors.white} />
            </View>
            <Text style={styles.userName}>Game Reviewer</Text>
            <Text style={styles.userSubtitle}>Professional Gaming Community</Text>
          </View>
        </LinearGradient>

        {/* App Stats */}
        {stats && (
          <View style={styles.statsSection}>
            <Text style={styles.sectionTitle}>Community Stats</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Icon name="videogame-asset" size={32} color={colors.primaryBlue} />
                <Text style={styles.statNumber}>{stats.total_games}</Text>
                <Text style={styles.statLabel}>Games</Text>
              </View>
              <View style={styles.statCard}>
                <Icon name="rate-review" size={32} color={colors.green} />
                <Text style={styles.statNumber}>{stats.total_reviews}</Text>
                <Text style={styles.statLabel}>Reviews</Text>
              </View>
              <View style={styles.statCard}>
                <Icon name="star" size={32} color={colors.gold} />
                <Text style={styles.statNumber}>{stats.overall_average_rating.toFixed(1)}</Text>
                <Text style={styles.statLabel}>Avg Rating</Text>
              </View>
            </View>
          </View>
        )}

        {/* Menu Items */}
        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.menuItem}
              onPress={item.onPress}
              activeOpacity={0.8}
            >
              <View style={[styles.menuIcon, { backgroundColor: item.color }]}>
                <Icon name={item.icon} size={24} color={colors.white} />
              </View>
              <View style={styles.menuContent}>
                <Text style={styles.menuTitle}>{item.title}</Text>
                <Text style={styles.menuSubtitle}>{item.subtitle}</Text>
              </View>
              <Icon name="chevron-right" size={24} color={colors.lightGray} />
            </TouchableOpacity>
          ))}
        </View>

        {/* App Info */}
        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>About</Text>
          <View style={styles.infoCard}>
            <Text style={styles.appName}>GameReview Pro</Text>
            <Text style={styles.appVersion}>Version 1.0.0</Text>
            <Text style={styles.appDescription}>
              A professional mobile application for discovering, reviewing, and sharing 
              gaming experiences. Join our community of passionate gamers and help others 
              find their next favorite game.
            </Text>
            
            <View style={styles.features}>
              <Text style={styles.featuresTitle}>Features:</Text>
              <Text style={styles.featureItem}>• Browse 50+ popular games</Text>
              <Text style={styles.featureItem}>• Write detailed reviews with pros/cons</Text>
              <Text style={styles.featureItem}>• Upload review images</Text>
              <Text style={styles.featureItem}>• Discover games by genre and platform</Text>
              <Text style={styles.featureItem}>• No registration required</Text>
            </View>
          </View>
        </View>

        {/* Social Links */}
        <View style={styles.socialSection}>
          <Text style={styles.sectionTitle}>Connect With Us</Text>
          {socialLinks.map((link, index) => (
            <TouchableOpacity
              key={index}
              style={styles.socialItem}
              onPress={() => openLink(link.url)}
              activeOpacity={0.8}
            >
              <Icon name={link.icon} size={24} color={colors.primaryBlue} />
              <Text style={styles.socialText}>{link.title}</Text>
              <Icon name="open-in-new" size={20} color={colors.lightGray} />
            </TouchableOpacity>
          ))}
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Made with ❤️ for the gaming community
          </Text>
          <Text style={styles.copyrightText}>
            © 2024 GameReview Pro. All rights reserved.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: layout.screenPadding,
    paddingTop: layout.xl,
    paddingBottom: layout.xl,
    borderBottomLeftRadius: layout.borderRadius * 2,
    borderBottomRightRadius: layout.borderRadius * 2,
  },
  profileInfo: {
    alignItems: 'center',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: layout.md,
  },
  userName: {
    ...typography.h2,
    color: colors.white,
    fontWeight: '700',
    marginBottom: layout.xs,
  },
  userSubtitle: {
    ...typography.body,
    color: colors.white,
    opacity: 0.8,
  },
  statsSection: {
    padding: layout.screenPadding,
  },
  sectionTitle: {
    ...typography.h3,
    color: colors.white,
    marginBottom: layout.md,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    padding: layout.md,
    alignItems: 'center',
    marginHorizontal: layout.xs,
    ...shadows.small,
  },
  statNumber: {
    ...typography.h2,
    color: colors.white,
    fontWeight: '700',
    marginTop: layout.sm,
  },
  statLabel: {
    ...typography.caption,
    color: colors.lightGray,
    marginTop: layout.xs,
  },
  menuSection: {
    padding: layout.screenPadding,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    padding: layout.md,
    marginBottom: layout.sm,
    ...shadows.small,
  },
  menuIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: layout.md,
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
  menuSubtitle: {
    ...typography.caption,
    color: colors.lightGray,
    marginTop: layout.xs / 2,
  },
  infoSection: {
    padding: layout.screenPadding,
  },
  infoCard: {
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    padding: layout.lg,
    ...shadows.small,
  },
  appName: {
    ...typography.h2,
    color: colors.white,
    fontWeight: '700',
    marginBottom: layout.xs,
  },
  appVersion: {
    ...typography.caption,
    color: colors.primaryBlue,
    marginBottom: layout.md,
  },
  appDescription: {
    ...typography.body,
    color: colors.lightGray,
    lineHeight: 22,
    marginBottom: layout.md,
  },
  features: {
    marginTop: layout.sm,
  },
  featuresTitle: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
    marginBottom: layout.sm,
  },
  featureItem: {
    ...typography.caption,
    color: colors.lightGray,
    marginBottom: layout.xs / 2,
  },
  socialSection: {
    padding: layout.screenPadding,
  },
  socialItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.darkGray,
    borderRadius: layout.borderRadius,
    padding: layout.md,
    marginBottom: layout.sm,
    ...shadows.small,
  },
  socialText: {
    ...typography.body,
    color: colors.white,
    flex: 1,
    marginLeft: layout.md,
  },
  footer: {
    padding: layout.screenPadding,
    paddingBottom: layout.xl,
    alignItems: 'center',
  },
  footerText: {
    ...typography.body,
    color: colors.lightGray,
    textAlign: 'center',
    marginBottom: layout.sm,
  },
  copyrightText: {
    ...typography.caption,
    color: colors.lightGray,
    textAlign: 'center',
  },
});

export default ProfileScreen;

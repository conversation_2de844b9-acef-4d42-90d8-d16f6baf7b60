"""
File handling utilities for GameReview Application
"""

import os
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename
from PIL import Image
from flask import current_app

def setup_directories():
    """Create necessary directories for file uploads"""
    try:
        upload_dir = current_app.config['UPLOAD_FOLDER']
        static_dir = current_app.config['STATIC_FOLDER']
    except RuntimeError:
        # Fallback for when called outside app context
        upload_dir = 'static/uploads'
        static_dir = 'static'

    os.makedirs(upload_dir, exist_ok=True)
    os.makedirs(os.path.join(static_dir, 'images'), exist_ok=True)

    print("✅ Directories setup completed")

def allowed_file(filename):
    """Check if uploaded file has allowed extension"""
    if not filename:
        return False
    
    allowed_extensions = current_app.config['ALLOWED_EXTENSIONS']
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def generate_unique_filename(original_filename):
    """Generate unique filename for uploaded files"""
    if not original_filename:
        return None
    
    # Get file extension
    ext = original_filename.rsplit('.', 1)[1].lower() if '.' in original_filename else ''
    
    # Generate unique filename with timestamp and UUID
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    unique_id = uuid.uuid4().hex[:8]
    
    return f"{timestamp}_{unique_id}.{ext}"

def optimize_image(image_path):
    """Optimize uploaded image for mobile app usage"""
    try:
        with Image.open(image_path) as img:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'P'):
                img = img.convert('RGB')
            
            # Get max width from config
            max_width = current_app.config.get('MAX_IMAGE_WIDTH', 1200)
            
            # Resize if too large
            if img.width > max_width:
                ratio = max_width / img.width
                new_height = int(img.height * ratio)
                img = img.resize((max_width, new_height), Image.Resampling.LANCZOS)
            
            # Get quality from config
            quality = current_app.config.get('IMAGE_QUALITY', 85)
            
            # Save optimized image
            img.save(image_path, 'JPEG', quality=quality, optimize=True)
            
        print(f"✅ Image optimized: {image_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error optimizing image {image_path}: {e}")
        return False

def save_uploaded_file(file):
    """Save uploaded file with optimization"""
    if not file or not file.filename:
        return None, "No file provided"
    
    if not allowed_file(file.filename):
        allowed_exts = ', '.join(current_app.config['ALLOWED_EXTENSIONS'])
        return None, f"Invalid file type. Allowed: {allowed_exts}"
    
    try:
        # Generate unique filename
        unique_filename = generate_unique_filename(file.filename)
        if not unique_filename:
            return None, "Could not generate filename"
        
        # Save file
        upload_folder = current_app.config['UPLOAD_FOLDER']
        filepath = os.path.join(upload_folder, unique_filename)
        file.save(filepath)
        
        # Optimize image
        if optimize_image(filepath):
            # Return relative path for database storage
            relative_path = f"/static/uploads/{unique_filename}"
            return relative_path, None
        else:
            # Remove file if optimization failed
            try:
                os.remove(filepath)
            except:
                pass
            return None, "Image optimization failed"
            
    except Exception as e:
        print(f"❌ Error saving file: {e}")
        return None, f"File save error: {str(e)}"

def delete_file(file_path):
    """Delete a file from the filesystem"""
    if not file_path:
        return True
    
    try:
        # Convert relative path to absolute
        if file_path.startswith('/static/'):
            file_path = file_path[1:]  # Remove leading slash
        
        full_path = os.path.join(os.getcwd(), file_path)
        
        if os.path.exists(full_path):
            os.remove(full_path)
            print(f"✅ File deleted: {full_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error deleting file {file_path}: {e}")
        return False

def get_file_size(file_path):
    """Get file size in bytes"""
    try:
        if file_path.startswith('/static/'):
            file_path = file_path[1:]
        
        full_path = os.path.join(os.getcwd(), file_path)
        return os.path.getsize(full_path) if os.path.exists(full_path) else 0
        
    except Exception:
        return 0

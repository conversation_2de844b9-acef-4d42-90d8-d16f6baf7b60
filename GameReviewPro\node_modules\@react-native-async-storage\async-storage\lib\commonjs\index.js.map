{"version": 3, "names": ["_AsyncStorage", "_interopRequireDefault", "require", "_hooks", "obj", "__esModule", "default", "_default", "AsyncStorage", "exports"], "sources": ["index.ts"], "sourcesContent": ["import AsyncStorage from \"./AsyncStorage\";\n\nexport { useAsyncStorage } from \"./hooks\";\n\nexport type { AsyncStorageStatic } from \"./types\";\n\nexport default AsyncStorage;\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAA0C,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,IAAAG,QAAA,GAI3BC,qBAAY;AAAAC,OAAA,CAAAH,OAAA,GAAAC,QAAA"}
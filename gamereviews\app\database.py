"""
Database initialization and management for GameReview App
"""

import sqlite3
import os
from flask import current_app

def get_db_connection():
    """Get database connection with row factory"""
    db_path = current_app.config.get('DATABASE_URL', 'database.db')
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """Initialize database with tables"""
    conn = get_db_connection()
    
    try:
        # Create games table
        conn.execute('''
            CREATE TABLE IF NOT EXISTS games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                genre TEXT,
                platforms TEXT,
                cover_image TEXT,
                average_rating REAL DEFAULT 0.0,
                total_reviews INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create reviews table
        conn.execute('''
            CREATE TABLE IF NOT EXISTS reviews (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id INTEGER,
                reviewer_name TEXT DEFAULT 'Anonymous',
                rating INTEGER NOT NULL,
                review_text TEXT,
                platform TEXT,
                pros TEXT,
                cons TEXT,
                image_path TEXT,
                likes_count INTEGER DEFAULT 0,
                views_count INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (game_id) REFERENCES games(id)
            )
        ''')
        
        # Create indexes for better performance
        conn.execute('CREATE INDEX IF NOT EXISTS idx_games_genre ON games(genre)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_games_rating ON games(average_rating)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_reviews_game_id ON reviews(game_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_reviews_rating ON reviews(rating)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_reviews_created_at ON reviews(created_at)')
        
        conn.commit()
        print("✅ Database initialized successfully!")
        
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        conn.rollback()
    finally:
        conn.close()

def update_game_rating(game_id):
    """Update game's average rating and total reviews count"""
    conn = get_db_connection()
    
    try:
        # Calculate new average rating
        result = conn.execute('''
            SELECT AVG(rating) as avg_rating, COUNT(*) as total_reviews
            FROM reviews 
            WHERE game_id = ?
        ''', (game_id,)).fetchone()
        
        avg_rating = result['avg_rating'] if result['avg_rating'] else 0.0
        total_reviews = result['total_reviews']
        
        # Update game record
        conn.execute('''
            UPDATE games 
            SET average_rating = ?, total_reviews = ?
            WHERE id = ?
        ''', (round(avg_rating, 1), total_reviews, game_id))
        
        conn.commit()
        
    except Exception as e:
        print(f"❌ Error updating game rating: {e}")
        conn.rollback()
    finally:
        conn.close()

def execute_query(query, params=()):
    """Execute SELECT query and return results"""
    conn = get_db_connection()
    try:
        cursor = conn.execute(query, params)
        results = cursor.fetchall()
        return results
    finally:
        conn.close()

def execute_update(query, params=()):
    """Execute INSERT/UPDATE/DELETE query and return affected rows"""
    conn = get_db_connection()
    try:
        cursor = conn.execute(query, params)
        conn.commit()
        return cursor.rowcount
    finally:
        conn.close()

def execute_insert(query, params=()):
    """Execute INSERT query and return last row ID"""
    conn = get_db_connection()
    try:
        cursor = conn.execute(query, params)
        conn.commit()
        return cursor.lastrowid
    finally:
        conn.close()

"""
API Routes for GameReview Mobile App
RESTful endpoints for games and reviews management
"""

from flask import request, jsonify, send_from_directory
from app import app, get_db_connection, update_game_rating, allowed_file
from models import Game, Review, DatabaseManager, GAMING_PLATFORMS, GAME_GENRES
from werkzeug.utils import secure_filename
from PIL import Image
import os
import uuid
import json

# Static file serving
@app.route('/static/<path:filename>')
def serve_static(filename):
    """Serve static files (images, uploads)"""
    return send_from_directory('static', filename)

# Health check endpoint
@app.route('/api/health')
def health_check():
    """API health check"""
    return jsonify({
        'status': 'healthy',
        'message': 'GameReview API is running',
        'version': '1.0.0'
    })

# Games API Endpoints
@app.route('/api/games', methods=['GET'])
def get_games():
    """Get all games with optional filtering"""
    try:
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        genre = request.args.get('genre')
        platform = request.args.get('platform')
        sort_by = request.args.get('sort', 'title')  # title, rating, reviews
        
        offset = (page - 1) * limit
        
        # Build query
        query = "SELECT * FROM games WHERE 1=1"
        params = []
        
        if genre:
            query += " AND genre LIKE ?"
            params.append(f"%{genre}%")
        
        if platform:
            query += " AND platforms LIKE ?"
            params.append(f"%{platform}%")
        
        # Add sorting
        if sort_by == 'rating':
            query += " ORDER BY average_rating DESC"
        elif sort_by == 'reviews':
            query += " ORDER BY total_reviews DESC"
        else:
            query += " ORDER BY title ASC"
        
        query += " LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        rows = DatabaseManager.execute_query(query, tuple(params))
        games = [Game.from_row(row).to_dict() for row in rows]
        
        # Get total count for pagination
        count_query = "SELECT COUNT(*) as total FROM games WHERE 1=1"
        count_params = []
        
        if genre:
            count_query += " AND genre LIKE ?"
            count_params.append(f"%{genre}%")
        
        if platform:
            count_query += " AND platforms LIKE ?"
            count_params.append(f"%{platform}%")
        
        total_result = DatabaseManager.execute_query(count_query, tuple(count_params))
        total_games = total_result[0]['total'] if total_result else 0
        
        return jsonify({
            'success': True,
            'games': games,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total_games,
                'pages': (total_games + limit - 1) // limit
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/games/<int:game_id>', methods=['GET'])
def get_game(game_id):
    """Get specific game by ID"""
    try:
        rows = DatabaseManager.execute_query(
            "SELECT * FROM games WHERE id = ?", (game_id,)
        )
        
        if not rows:
            return jsonify({'success': False, 'error': 'Game not found'}), 404
        
        game = Game.from_row(rows[0]).to_dict()
        
        return jsonify({
            'success': True,
            'game': game
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/games/search', methods=['GET'])
def search_games():
    """Search games by title"""
    try:
        query = request.args.get('q', '').strip()
        if not query:
            return jsonify({'success': False, 'error': 'Search query required'}), 400
        
        rows = DatabaseManager.execute_query(
            "SELECT * FROM games WHERE title LIKE ? ORDER BY title ASC",
            (f"%{query}%",)
        )
        
        games = [Game.from_row(row).to_dict() for row in rows]
        
        return jsonify({
            'success': True,
            'games': games,
            'query': query
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/games/top-rated', methods=['GET'])
def get_top_rated_games():
    """Get top rated games"""
    try:
        limit = int(request.args.get('limit', 10))
        
        rows = DatabaseManager.execute_query(
            """SELECT * FROM games 
               WHERE total_reviews > 0 
               ORDER BY average_rating DESC, total_reviews DESC 
               LIMIT ?""",
            (limit,)
        )
        
        games = [Game.from_row(row).to_dict() for row in rows]
        
        return jsonify({
            'success': True,
            'games': games
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Reviews API Endpoints
@app.route('/api/reviews', methods=['GET'])
def get_reviews():
    """Get latest reviews"""
    try:
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        game_id = request.args.get('game_id')
        
        offset = (page - 1) * limit
        
        # Build query with game information
        query = """
            SELECT r.*, g.title as game_title, g.cover_image as game_cover
            FROM reviews r
            JOIN games g ON r.game_id = g.id
            WHERE 1=1
        """
        params = []
        
        if game_id:
            query += " AND r.game_id = ?"
            params.append(game_id)
        
        query += " ORDER BY r.created_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        rows = DatabaseManager.execute_query(query, tuple(params))
        
        reviews = []
        for row in rows:
            review = Review.from_row(row).to_dict()
            review['game_title'] = row['game_title']
            review['game_cover'] = row['game_cover']
            reviews.append(review)
        
        return jsonify({
            'success': True,
            'reviews': reviews
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reviews/<int:review_id>', methods=['GET'])
def get_review(review_id):
    """Get specific review by ID and increment view count"""
    try:
        # Increment view count
        DatabaseManager.execute_update(
            "UPDATE reviews SET views_count = views_count + 1 WHERE id = ?",
            (review_id,)
        )

        # Get review with game info
        rows = DatabaseManager.execute_query(
            """SELECT r.*, g.title as game_title, g.cover_image as game_cover
               FROM reviews r
               JOIN games g ON r.game_id = g.id
               WHERE r.id = ?""",
            (review_id,)
        )

        if not rows:
            return jsonify({'success': False, 'error': 'Review not found'}), 404

        row = rows[0]
        review = Review.from_row(row).to_dict()
        review['game_title'] = row['game_title']
        review['game_cover'] = row['game_cover']

        return jsonify({
            'success': True,
            'review': review
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reviews', methods=['POST'])
def create_review():
    """Create new review"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['game_id', 'rating']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'{field} is required'}), 400

        # Validate rating
        rating = int(data['rating'])
        if rating < 1 or rating > 5:
            return jsonify({'success': False, 'error': 'Rating must be between 1 and 5'}), 400

        # Check if game exists
        game_rows = DatabaseManager.execute_query(
            "SELECT id FROM games WHERE id = ?", (data['game_id'],)
        )
        if not game_rows:
            return jsonify({'success': False, 'error': 'Game not found'}), 404

        # Create review
        review_id = DatabaseManager.execute_insert(
            """INSERT INTO reviews
               (game_id, reviewer_name, rating, review_text, platform, pros, cons)
               VALUES (?, ?, ?, ?, ?, ?, ?)""",
            (
                data['game_id'],
                data.get('reviewer_name', 'Anonymous'),
                rating,
                data.get('review_text', ''),
                data.get('platform', ''),
                '|'.join(data.get('pros', [])) if data.get('pros') else '',
                '|'.join(data.get('cons', [])) if data.get('cons') else ''
            )
        )

        # Update game rating
        update_game_rating(data['game_id'])

        return jsonify({
            'success': True,
            'review_id': review_id,
            'message': 'Review created successfully'
        }), 201

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reviews/<int:review_id>/like', methods=['PUT'])
def like_review(review_id):
    """Like a review (increment like count)"""
    try:
        # Check if review exists
        rows = DatabaseManager.execute_query(
            "SELECT id FROM reviews WHERE id = ?", (review_id,)
        )
        if not rows:
            return jsonify({'success': False, 'error': 'Review not found'}), 404

        # Increment like count
        DatabaseManager.execute_update(
            "UPDATE reviews SET likes_count = likes_count + 1 WHERE id = ?",
            (review_id,)
        )

        # Get updated like count
        updated_rows = DatabaseManager.execute_query(
            "SELECT likes_count FROM reviews WHERE id = ?", (review_id,)
        )
        new_likes_count = updated_rows[0]['likes_count']

        return jsonify({
            'success': True,
            'likes_count': new_likes_count,
            'message': 'Review liked successfully'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reviews/game/<int:game_id>', methods=['GET'])
def get_reviews_for_game(game_id):
    """Get all reviews for a specific game"""
    try:
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        sort_by = request.args.get('sort', 'newest')  # newest, oldest, rating_high, rating_low, likes

        offset = (page - 1) * limit

        # Build query with sorting
        query = "SELECT * FROM reviews WHERE game_id = ?"
        params = [game_id]

        if sort_by == 'oldest':
            query += " ORDER BY created_at ASC"
        elif sort_by == 'rating_high':
            query += " ORDER BY rating DESC, created_at DESC"
        elif sort_by == 'rating_low':
            query += " ORDER BY rating ASC, created_at DESC"
        elif sort_by == 'likes':
            query += " ORDER BY likes_count DESC, created_at DESC"
        else:  # newest (default)
            query += " ORDER BY created_at DESC"

        query += " LIMIT ? OFFSET ?"
        params.extend([limit, offset])

        rows = DatabaseManager.execute_query(query, tuple(params))
        reviews = [Review.from_row(row).to_dict() for row in rows]

        return jsonify({
            'success': True,
            'reviews': reviews,
            'game_id': game_id
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Image Upload Endpoints
@app.route('/api/upload/review-image', methods=['POST'])
def upload_review_image():
    """Upload image for review"""
    try:
        if 'image' not in request.files:
            return jsonify({'success': False, 'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400

        if not allowed_file(file.filename):
            return jsonify({'success': False, 'error': 'Invalid file type. Allowed: png, jpg, jpeg, gif, webp'}), 400

        # Generate unique filename
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

        # Save and optimize image
        file.save(filepath)

        # Optimize image size for mobile
        try:
            with Image.open(filepath) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'P'):
                    img = img.convert('RGB')

                # Resize if too large (max 1200px width)
                if img.width > 1200:
                    ratio = 1200 / img.width
                    new_height = int(img.height * ratio)
                    img = img.resize((1200, new_height), Image.Resampling.LANCZOS)

                # Save optimized image
                img.save(filepath, 'JPEG', quality=85, optimize=True)
        except Exception as img_error:
            print(f"Image optimization error: {img_error}")

        image_url = f"/static/uploads/{unique_filename}"

        return jsonify({
            'success': True,
            'image_url': image_url,
            'filename': unique_filename
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reviews/<int:review_id>/image', methods=['PUT'])
def add_image_to_review(review_id):
    """Add image to existing review"""
    try:
        data = request.get_json()
        image_path = data.get('image_path')

        if not image_path:
            return jsonify({'success': False, 'error': 'image_path is required'}), 400

        # Check if review exists
        rows = DatabaseManager.execute_query(
            "SELECT id FROM reviews WHERE id = ?", (review_id,)
        )
        if not rows:
            return jsonify({'success': False, 'error': 'Review not found'}), 404

        # Update review with image
        DatabaseManager.execute_update(
            "UPDATE reviews SET image_path = ? WHERE id = ?",
            (image_path, review_id)
        )

        return jsonify({
            'success': True,
            'message': 'Image added to review successfully'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Utility Endpoints
@app.route('/api/platforms', methods=['GET'])
def get_platforms():
    """Get list of gaming platforms"""
    return jsonify({
        'success': True,
        'platforms': GAMING_PLATFORMS
    })

@app.route('/api/genres', methods=['GET'])
def get_genres():
    """Get list of game genres"""
    return jsonify({
        'success': True,
        'genres': GAME_GENRES
    })

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """Get app statistics"""
    try:
        # Get total games
        games_result = DatabaseManager.execute_query("SELECT COUNT(*) as total FROM games")
        total_games = games_result[0]['total'] if games_result else 0

        # Get total reviews
        reviews_result = DatabaseManager.execute_query("SELECT COUNT(*) as total FROM reviews")
        total_reviews = reviews_result[0]['total'] if reviews_result else 0

        # Get average rating across all games
        avg_result = DatabaseManager.execute_query(
            "SELECT AVG(average_rating) as avg FROM games WHERE total_reviews > 0"
        )
        overall_avg_rating = round(avg_result[0]['avg'], 1) if avg_result and avg_result[0]['avg'] else 0.0

        return jsonify({
            'success': True,
            'stats': {
                'total_games': total_games,
                'total_reviews': total_reviews,
                'overall_average_rating': overall_avg_rating
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

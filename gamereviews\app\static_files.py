"""
Static file serving for GameReview Application
"""

from flask import Blueprint, send_from_directory, current_app
import os

# Create static files blueprint
static_bp = Blueprint('static_files', __name__)

@static_bp.route('/static/<path:filename>')
def serve_static(filename):
    """Serve static files (images, uploads)"""
    try:
        static_folder = current_app.config.get('STATIC_FOLDER', 'static')
        return send_from_directory(static_folder, filename)
    except FileNotFoundError:
        # Return a placeholder or 404
        return "File not found", 404

@static_bp.route('/static/uploads/<path:filename>')
def serve_uploads(filename):
    """Serve uploaded files"""
    try:
        upload_folder = current_app.config.get('UPLOAD_FOLDER', 'static/uploads')
        return send_from_directory(upload_folder, filename)
    except FileNotFoundError:
        return "File not found", 404

@static_bp.route('/static/images/<path:filename>')
def serve_images(filename):
    """Serve game cover images"""
    try:
        images_folder = os.path.join(current_app.config.get('STATIC_FOLDER', 'static'), 'images')
        return send_from_directory(images_folder, filename)
    except FileNotFoundError:
        return "Image not found", 404

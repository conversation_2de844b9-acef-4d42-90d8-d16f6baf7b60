#!/usr/bin/env python3
"""
GameReview Pro - Python Desktop Application
Main entry point for the AI-powered game review platform
"""

import sys
import os
import logging
from datetime import datetime

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gamereviews.log'),
        logging.StreamHandler()
    ]
)

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'customtkinter', 'torch', 'transformers', 'sqlalchemy',
        'pillow', 'opencv-python', 'numpy', 'pandas', 'matplotlib',
        'scikit-learn', 'nltk', 'textblob', 'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'opencv-python':
                import cv2
            elif package == 'pillow':
                import PIL
            else:
                __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def initialize_app():
    """Initialize the application"""
    print("🎮 GameReview Pro - Python Desktop Application")
    print("=" * 60)
    print("🔧 Initializing application...")
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Dependency check failed. Please install missing packages.")
        return False
    
    print("✅ All dependencies found")
    
    # Initialize database
    try:
        from database.models import db_manager
        print("✅ Database initialized")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False
    
    # Initialize AI components
    try:
        from ai.sentiment_analyzer import sentiment_analyzer
        from ai.recommendation_engine import recommendation_engine
        print("✅ AI components initialized")
    except Exception as e:
        print(f"⚠️ AI initialization warning: {e}")
        print("   App will continue with limited AI features")
    
    # Load sample data if needed
    try:
        from utils.sample_data_loader import load_sample_data
        load_sample_data()
        print("✅ Sample data loaded")
    except Exception as e:
        print(f"⚠️ Sample data loading warning: {e}")
        print("   App will continue with empty database")
    
    return True

def main():
    """Main application entry point"""
    try:
        # Initialize application
        if not initialize_app():
            print("❌ Application initialization failed")
            sys.exit(1)
        
        # Start GUI
        print("🚀 Starting GUI...")
        from gui.main_window import GameReviewProApp
        
        app = GameReviewProApp()
        app.run()
        
    except KeyboardInterrupt:
        print("\n👋 Application interrupted by user")
        sys.exit(0)
    except Exception as e:
        logging.error(f"Application error: {e}")
        print(f"❌ Application error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
